<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="bcc0145d-414e-4e58-b4c4-296a787dcd86" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 4
}]]></component>
  <component name="ProjectId" id="2zk5A4eAqIghgDgY0RfG2KmawDq" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Gradle.C:/Users/<USER>/OneDrive/Asztali gép/criptyx-client/build.gradle.executor": "Run",
    "Gradle.criptyx-client [:Start.main()].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "ignore.virus.scanning.warn.message": "true",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0"
  }
}]]></component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="bcc0145d-414e-4e58-b4c4-296a787dcd86" name="Changes" comment="" />
      <created>1752262084334</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752262084334</updated>
    </task>
    <servers />
  </component>
</project>