###############################################################################
# Sample configuration for OptiFine's Dynamic Lights feature.
###############################################################################
# dynamic_lights.properties
###############################################################################
# This file is offered without any copyright restrictions. 
# Please copy and modify it to suit your needs.
#
# This configuration file allows mods to define dynamic light levels for entities and items.
# Location: "/assets/<mod_id>/optifine/dynamic_lights.properties"

# Entity light levels
# The entity name is automatically expanded with the mod_id.
# The light level should be between 0 and 15. 
# For exaple:
#   entities=basalz:15 blitz:7
entities=<entity:light> ...  

# Item light levels
# The item name is automatically expanded with the mod_id.
# The light level should be between 0 and 15. 
# For exaple:
#   items=florb:15 morb:7
items=<item:light> ...  
 