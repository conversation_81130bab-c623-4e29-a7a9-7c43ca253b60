{"blend": {"func": "add", "srcrgb": "one", "dstrgb": "zero"}, "vertex": "sobel", "fragment": "color_convolve", "attributes": ["Position"], "samplers": [{"name": "DiffuseSampler"}], "uniforms": [{"name": "ProjMat", "type": "matrix4x4", "count": 16, "values": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]}, {"name": "InSize", "type": "float", "count": 2, "values": [1.0, 1.0]}, {"name": "OutSize", "type": "float", "count": 2, "values": [1.0, 1.0]}, {"name": "<PERSON>", "type": "float", "count": 3, "values": [0.3, 0.59, 0.11]}, {"name": "RedMatrix", "type": "float", "count": 3, "values": [1.0, 0.0, 0.0]}, {"name": "GreenMatrix", "type": "float", "count": 3, "values": [0.0, 1.0, 0.0]}, {"name": "BlueMatrix", "type": "float", "count": 3, "values": [0.0, 0.0, 1.0]}, {"name": "Offset", "type": "float", "count": 3, "values": [0.0, 0.0, 0.0]}, {"name": "ColorScale", "type": "float", "count": 3, "values": [1.0, 1.0, 1.0]}, {"name": "Saturation", "type": "float", "count": 1, "values": [1.8]}]}