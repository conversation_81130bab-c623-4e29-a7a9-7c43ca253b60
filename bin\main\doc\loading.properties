###############################################################################
# Sample configuration for OptiFine's Custom Loading Screens feature.
###############################################################################
# loading.properties
###############################################################################
# This file is offered without any copyright restrictions. 
# Please copy and modify it to suit your needs.
#
# Location: "/assets/minecraft/optifine/gui/loading/loading.properties"
# Controls the behaviour of the world loading screen
#
# Custom loading screen backgrounds per dimension can be defined as:
#    /assets/minecraft/optifine/gui/loading/background<dim>.png
# where "dim" is the dimension id:
#   - nether    = -1
#   - overworld = 0
#   - the end   = 1
#
# Modded dimensions can also be configured in this way. 

# Scale mode (optional)
# Custom scale mode for the background texture.
# Values
#   fixed - use fixed scale (default)
#   full - full screen, keep aspect ratio
#   stretch - fullscreen
scaleMode=<fixed|full|stretch>

# Scale (optional)
# Custom scale for the background texture.
# For scale mode "fixed" it defines the scale to use (default is 2). 
# This is combined with the curent GUI scale.
# For scale modes "full" and "stretch" it defines how many full textures should 
# fit on the screen (default is 1).
scale=2

# Center (optional)
# Defines if the background texture should be centered on the screen.
# Default is false.
center=<true|false>

# The properties "scaleMode", "scale" and "center" can also be configured per dimension
dim<dim>.scaleMode=<fixed|full|stretch>
dim<dim>.scale=2
dim<dim>.center=<true|false>
 