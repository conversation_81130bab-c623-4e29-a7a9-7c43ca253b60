# Traduit en français par Z0ul0u25, <PERSON><PERSON><PERSON>, SkytAsul, robot275 et Calinou

# General
of.general.ambiguous=Ambiguë
of.general.custom=Personnalisé
of.general.from=De
of.general.id=ID
of.general.restart=Redémarrer
of.general.smart=Approfondis

# Keys
of.key.zoom=Zoom

# Message
of.message.aa.shaders1=L'anticrénelage n'est pas compatible avec les shaders.
of.message.aa.shaders2=Veuillez désactiver les shaders pour activer cette option.

of.message.af.shaders1=Le filtrage anisotrope n'est pas compatible avec les shaders.
of.message.af.shaders2=Veuillez désactiver les shaders pour activer cette option.

of.message.fr.shaders1=Le rendu rapide n'est pas compatible avec les shaders.
of.message.fr.shaders2=Veuillez désactiver les shaders pour activer cette option.

of.message.an.shaders1=L'anaglyphe 3D n'est pas compatible avec les shaders.
of.message.an.shaders2=Veuillez désactiver les shaders pour activer cette option.

of.message.shaders.aa1=Les shaders ne sont pas compatibles avec l'anticrénelage.
of.message.shaders.aa2=Veuillez définir Qualité -> Anticrénelage sur "Non" puis redémarrez le jeu.

of.message.shaders.af1=Les shaders ne sont pas compatibles avec le filtrage anisotrope.
of.message.shaders.af2=Veuillez définir Qualité -> Filtrage anisotrope sur "Non".

of.message.shaders.fr1=Les shaders ne sont pas compatibles avec le rendu rapide.
of.message.shaders.fr2=Veuillez définir Performance -> Rendu rapide sur "Non".

of.message.shaders.an1=Les shaders ne sont pas compatibles avec la 3D anaglyphe.
of.message.shaders.an2=Veuillez définir Autre -> 3D anaglyphe sur "Non".

of.message.shaders.nv1=Ce pack de shaders nécessite une version d'OptiFine plus récente : %s
of.message.shaders.nv2=Voulez-vous vraiment continuer ?

of.message.newVersion=Une nouvelle version d'§eOptiFine§f est disponible : §e%s§f
of.message.java64Bit=Pour de meilleures performances, installez une version §e64 bits de Java§f
of.message.openglError=§eErreur OpenGL§f : %s (%s)

of.message.shaders.loading=Chargement des shaders : %s

of.message.other.reset=Réinitialiser tous les réglages vidéo à leurs valeurs par défaut ?

of.message.loadingVisibleChunks=Chargement des tronçons visibles

# Skin customization

of.options.skinCustomisation.ofCape=Cape OptiFine...

of.options.capeOF.title=Cape OptiFine
of.options.capeOF.openEditor=Ouvrir l'éditeur de cape
of.options.capeOF.reloadCape=Recharger la cape
of.options.capeOF.copyEditorLink=Copier le lien

of.message.capeOF.openEditor=L'éditeur de cape OptiFine devrait s'ouvrir dans un navigateur internet.
of.message.capeOF.openEditorError=Erreur lors de l'ouverture du lien de l'éditeur dans un navigateur internet.
of.message.capeOF.reloadCape=La cape sera rechargée dans 15 secondes.

of.message.capeOF.error1=Échec de l'authentification Mojang.
of.message.capeOF.error2=Erreur : %s

# Video settings

options.graphics.tooltip.1=Graphismes
options.graphics.tooltip.2=  Rapides - Basse qualité (rapide)
options.graphics.tooltip.3=  Détaillés - Haute qualité (lent)
options.graphics.tooltip.4=Modifie l'apparence des nuages, des feuilles, de l'eau, des
options.graphics.tooltip.5=ombres et du côté de l'herbe.

of.options.renderDistance.tiny=Minimale
of.options.renderDistance.short=Courte
of.options.renderDistance.normal=Normale
of.options.renderDistance.far=Lointaine
of.options.renderDistance.extreme=Extrême
of.options.renderDistance.insane=Insensée
of.options.renderDistance.ludicrous=Inimaginable

options.renderDistance.tooltip.1=Distance d'affichage
options.renderDistance.tooltip.2=  2 Minimale - 32m (très rapide)
options.renderDistance.tooltip.3=  8 Normale - 128m (normal)
options.renderDistance.tooltip.4=  16 Lointaine - 256m (lent)
options.renderDistance.tooltip.5=  32 Extrême - 512m (très lent) - Très gourmand en ressources
options.renderDistance.tooltip.6=  48 Insensée - 768m, nécessite 2 Go de RAM allouée
options.renderDistance.tooltip.7=  64 Inimaginable - 1024m, nécessite 3 Go de RAM allouée
options.renderDistance.tooltip.8=Les valeurs au-dessus de 16 n'ont effet qu'en solo.

options.ao.tooltip.1=Luminosité adoucie
options.ao.tooltip.2=  Non - aucun adoucissement (rapide)
options.ao.tooltip.3=  Minimum - Adoucissement simple (lent)
options.ao.tooltip.4=  Maximum - Adoucissement complexe (très lent)

options.framerateLimit.tooltip.1=FPS max
options.framerateLimit.tooltip.2=  VSync - limite de l'écran (60, 30, 20)
options.framerateLimit.tooltip.3=  5-255 - variable
options.framerateLimit.tooltip.4=  Illimitée - sans limite (très rapide)
options.framerateLimit.tooltip.5=La limite de FPS diminue le taux d'images par seconde
options.framerateLimit.tooltip.6=même si la valeur limite n'est pas atteinte.
of.options.framerateLimit.vsync=VSync

of.options.AO_LEVEL=Ombres adoucies 
of.options.AO_LEVEL.tooltip.1=Ombres adoucies
of.options.AO_LEVEL.tooltip.2=  Non - aucune ombre
of.options.AO_LEVEL.tooltip.3=  50 %% - légères ombres
of.options.AO_LEVEL.tooltip.4=  100 %% - fortes ombres

options.viewBobbing.tooltip.1=Mouvements plus réalistes.
options.viewBobbing.tooltip.2=Lors de l'utilisation du mipmap, définir sur "Non" pour de
options.viewBobbing.tooltip.3=meilleurs résultats.

options.guiScale.tooltip.1=Taille de l'interface
options.guiScale.tooltip.2=  Auto - taille maximale
options.guiScale.tooltip.3=  Petite, Normale, Grande - 1x à 3x
options.guiScale.tooltip.4=  4x à 10x - Disponible pour les écrans 4K
options.guiScale.tooltip.5=Les valeurs impaires (1x, 3x, 5x ...) ne sont pas
options.guiScale.tooltip.6=compatibles avec l'Unicode.
options.guiScale.tooltip.7=Une petite interface peut rendre le jeu plus rapide.

options.vbo=Utiliser les VBOs 
options.vbo.tooltip.1=Utiliser les VBOs (objets en mémoire tampon)
options.vbo.tooltip.2=Utilise un autre modèle de rendu qui est habituellement
options.vbo.tooltip.3=plus rapide (5-10 %%) que le rendu par défaut.

options.gamma.tooltip.1=Change la luminosité des endroits sombres
options.gamma.tooltip.2=  Sombre - luminosité standard
options.gamma.tooltip.3=  1-99 %% - variable
options.gamma.tooltip.4=  Vive - luminosité maximale pour les objets sombres
options.gamma.tooltip.5=Cette option ne change pas la luminosité des objets
options.gamma.tooltip.6=complètement noirs.

options.anaglyph.tooltip.1=3D anaglyphe 
options.anaglyph.tooltip.2=Active un effet stéréoscopique 3D utilisant
options.anaglyph.tooltip.3=différentes couleurs pour chaque œil.
options.anaglyph.tooltip.4=Nécessite des lunettes rouge-cyan pour une vue adaptée.

options.attackIndicator.tooltip.1=Configure la position de l'indicateur d'attaque
options.attackIndicator.tooltip.2=  Viseur - sous le viseur
options.attackIndicator.tooltip.3=  Barre - à côté de la barre
options.attackIndicator.tooltip.4=  Non - pas d'indicateur d'attaque
options.attackIndicator.tooltip.5=L'indicateur montre la force d'attaque de l'objet en main.

of.options.ALTERNATE_BLOCKS=Blocs alternatifs 
of.options.ALTERNATE_BLOCKS.tooltip.1=Blocs alternatifs
of.options.ALTERNATE_BLOCKS.tooltip.2=Utilise des modèles alternatifs pour certains blocs.
of.options.ALTERNATE_BLOCKS.tooltip.3=Dépend du pack de ressources sélectionné.

of.options.FOG_FANCY=Brouillard 
of.options.FOG_FANCY.tooltip.1=Type de brouillard
of.options.FOG_FANCY.tooltip.2=  Rapide - brouillard rapide
of.options.FOG_FANCY.tooltip.3=  Détaillé - meilleure apparence (plus lent)
of.options.FOG_FANCY.tooltip.4=  Non - Aucun brouillard (très rapide)
of.options.FOG_FANCY.tooltip.5=Le brouillard détaillé n'est disponible que si la carte
of.options.FOG_FANCY.tooltip.6=graphique le prend en charge.

of.options.FOG_START=Début du brouillard 
of.options.FOG_START.tooltip.1=Début du brouillard
of.options.FOG_START.tooltip.2=  0.2 - Le brouillard commence près du joueur
of.options.FOG_START.tooltip.3=  0.8 - Le brouillard commence loin du joueur
of.options.FOG_START.tooltip.4=Ceci n'affecte généralement pas les performances.

of.options.CHUNK_LOADING=Chargement des tronçons 
of.options.CHUNK_LOADING.tooltip.1=Chargement des tronçons
of.options.CHUNK_LOADING.tooltip.2=  Défaut - FPS instable lors du chargement des tronçons
of.options.CHUNK_LOADING.tooltip.3=  Adouci - FPS stable
of.options.CHUNK_LOADING.tooltip.4=  Multi-coeur - FPS stable, chargement 3x plus rapide
of.options.CHUNK_LOADING.tooltip.5=Adouci et Multi-coeur permettent d'éviter le ralentissement
of.options.CHUNK_LOADING.tooltip.6=et les lags causés par le chargement des tronçons.
of.options.CHUNK_LOADING.tooltip.7=Multi-coeur peut augmenter de 3x la vitesse de chargement
of.options.CHUNK_LOADING.tooltip.8=et augmenter les FPS en utilisant un autre coeur du processeur.
of.options.chunkLoading.smooth=Adouci
of.options.chunkLoading.multiCore=Multi-coeur

of.options.shaders=Shaders...
of.options.shadersTitle=Shaders

of.options.shaders.packNone=DÉSACTIVÉS
of.options.shaders.packDefault=(interne)

of.options.shaders.ANTIALIASING=Anticrénelage 
of.options.shaders.ANTIALIASING.tooltip.1=Anticrénelage
of.options.shaders.ANTIALIASING.tooltip.2=  Non - (par défaut) pas d'anticrénelage (rapide)
of.options.shaders.ANTIALIASING.tooltip.3=  FXAA 2x, 4x - anticrénelage des lignes et bords (lent)
of.options.shaders.ANTIALIASING.tooltip.4=FXAA est un effet de post-traitement qui adoucit les
of.options.shaders.ANTIALIASING.tooltip.5=lignes rugueuses et transitions rapides de couleur.
of.options.shaders.ANTIALIASING.tooltip.6=Il est plus rapide que l'anticrénelage traditionnel
of.options.shaders.ANTIALIASING.tooltip.7=et est compatible avec les shaders et le rendu rapide.

of.options.shaders.NORMAL_MAP=Carte normale 
of.options.shaders.NORMAL_MAP.tooltip.1=Carte normale
of.options.shaders.NORMAL_MAP.tooltip.2=  Oui - (par défaut) Active les cartes normales
of.options.shaders.NORMAL_MAP.tooltip.3=  Non - Désactive les cartes normales
of.options.shaders.NORMAL_MAP.tooltip.4=Les cartes normales peuvent être utilisées par les shaders
of.options.shaders.NORMAL_MAP.tooltip.5=pour simuler la géométrie 3D sur des surfaces de modèles plats.
of.options.shaders.NORMAL_MAP.tooltip.6=Les textures de cartes normales sont fournies par
of.options.shaders.NORMAL_MAP.tooltip.7=le pack de ressources utilisé.

of.options.shaders.SPECULAR_MAP=Carte spéculaire 
of.options.shaders.SPECULAR_MAP.tooltip.1=Carte spéculaire
of.options.shaders.SPECULAR_MAP.tooltip.2=  Oui - (par défaut) Active les cartes spéculaires
of.options.shaders.SPECULAR_MAP.tooltip.3=  Non - Désactive les cartes spéculaires
of.options.shaders.SPECULAR_MAP.tooltip.4=Les cartes spéculaires peuvent être utilisées par les shaders
of.options.shaders.SPECULAR_MAP.tooltip.5=pour simuler des reflets de lumière sur les surfaces.
of.options.shaders.SPECULAR_MAP.tooltip.6=Les textures de cartes spéculaires sont fournies par
of.options.shaders.SPECULAR_MAP.tooltip.7=le pack de ressources utilisé.

of.options.shaders.RENDER_RES_MUL=Qualité du rendu 
of.options.shaders.RENDER_RES_MUL.tooltip.1=Qualité du rendu
of.options.shaders.RENDER_RES_MUL.tooltip.2=  0.5x - faible (rapide)
of.options.shaders.RENDER_RES_MUL.tooltip.3=  1x - standard (par défaut)
of.options.shaders.RENDER_RES_MUL.tooltip.4=  2x - haute (lent)
of.options.shaders.RENDER_RES_MUL.tooltip.5=La qualité du rendu contrôle la taille des textures
of.options.shaders.RENDER_RES_MUL.tooltip.6=dont les shaders vont faire le rendu.
of.options.shaders.RENDER_RES_MUL.tooltip.7=Des valeurs faibles peuvent être utiles pour les écrans 4K.
of.options.shaders.RENDER_RES_MUL.tooltip.8=Des valeurs hautes servent de filtre d'anticrénelage.

of.options.shaders.SHADOW_RES_MUL=Qualité des ombres 
of.options.shaders.SHADOW_RES_MUL.tooltip.1=Qualité des ombres
of.options.shaders.SHADOW_RES_MUL.tooltip.2=  0.5x - faible (rapide)
of.options.shaders.SHADOW_RES_MUL.tooltip.3=  1x - standard (par défaut)
of.options.shaders.SHADOW_RES_MUL.tooltip.4=  2x - haute (lent)
of.options.shaders.SHADOW_RES_MUL.tooltip.5=La qualité des ombres contrôle la taille de la texture
of.options.shaders.SHADOW_RES_MUL.tooltip.6=de la map des ombres utilisée par les shaders.
of.options.shaders.SHADOW_RES_MUL.tooltip.7=Valeurs faibles = ombres grossières, inexactes.
of.options.shaders.SHADOW_RES_MUL.tooltip.8=Valeurs hautes = ombres subtiles, détaillées.

of.options.shaders.HAND_DEPTH_MUL=Profondeur de main 
of.options.shaders.HAND_DEPTH_MUL.tooltip.1=Profondeur de la main
of.options.shaders.HAND_DEPTH_MUL.tooltip.2=  0.5x - Main proche de la caméra
of.options.shaders.HAND_DEPTH_MUL.tooltip.3=  1x - (par défaut)
of.options.shaders.HAND_DEPTH_MUL.tooltip.4=  2x - Main éloignée de la caméra
of.options.shaders.HAND_DEPTH_MUL.tooltip.5=La profondeur de la main contrôle l'éloignement des
of.options.shaders.HAND_DEPTH_MUL.tooltip.6=objets tenus, par rapport à la caméra.
of.options.shaders.HAND_DEPTH_MUL.tooltip.7=Cela devrait changer le flou des objets en main
of.options.shaders.HAND_DEPTH_MUL.tooltip.8=pour les shaders avec un flou de profondeur de champ.

of.options.shaders.CLOUD_SHADOW=Ombre des nuages

of.options.shaders.OLD_HAND_LIGHT=Ancien éclairage de main 
of.options.shaders.OLD_HAND_LIGHT.tooltip.1=Ancien éclairage de la main
of.options.shaders.OLD_HAND_LIGHT.tooltip.2=  Par défaut - géré par le shader
of.options.shaders.OLD_HAND_LIGHT.tooltip.3=  Oui - utilise l'ancien éclairage de la main
of.options.shaders.OLD_HAND_LIGHT.tooltip.4=  Non - utilise le nouvel éclairage de la main
of.options.shaders.OLD_HAND_LIGHT.tooltip.5=Ce réglage permet aux shaders qui ne reconnaissent que
of.options.shaders.OLD_HAND_LIGHT.tooltip.6=les objets émettant de la lumière depuis la main principale
of.options.shaders.OLD_HAND_LIGHT.tooltip.7=de marcher avec les objets dans la seconde main.

of.options.shaders.OLD_LIGHTING=Ancien éclairage 
of.options.shaders.OLD_LIGHTING.tooltip.1=Ancien éclairage
of.options.shaders.OLD_LIGHTING.tooltip.2=  Par défaut - géré par le shader
of.options.shaders.OLD_LIGHTING.tooltip.3=  Oui - utilise l'ancien éclairage
of.options.shaders.OLD_LIGHTING.tooltip.4=  Non - n'utilise pas l'ancien éclairage
of.options.shaders.OLD_LIGHTING.tooltip.5=L'ancien éclairage contrôle l'éclairage fixe imposé par
of.options.shaders.OLD_LIGHTING.tooltip.6=défaut aux côtés des blocs.
of.options.shaders.OLD_LIGHTING.tooltip.7=Les shaders avec des ombres apportent généralement
of.options.shaders.OLD_LIGHTING.tooltip.8=un éclairage bien meilleur, selon la position du soleil.

of.options.shaders.DOWNLOAD=Télécharger les shaders 
of.options.shaders.DOWNLOAD.tooltip.1=Télécharger les shaders
of.options.shaders.DOWNLOAD.tooltip.2= 
of.options.shaders.DOWNLOAD.tooltip.3=Ouvre la page des packs de shaders dans un navigateur.
of.options.shaders.DOWNLOAD.tooltip.4=Mettez les packs téléchargés dans le "Dossier des shaders"
of.options.shaders.DOWNLOAD.tooltip.5=et ils apparaîtront dans la liste des shaders installés.

of.options.shaders.SHADER_PACK=Pack de shaders

of.options.shaders.shadersFolder=Dossier des shaders
of.options.shaders.shaderOptions=Options du shader...

of.options.shaderOptionsTitle=Options du shader

of.options.quality=Qualité...
of.options.qualityTitle=Paramètres de la qualité

of.options.details=Détails...
of.options.detailsTitle=Paramètres des détails

of.options.performance=Performances...
of.options.performanceTitle=Paramètres des performances

of.options.animations=Animations...
of.options.animationsTitle=Paramètres des animations

of.options.other=Autres...
of.options.otherTitle=Autres paramètres

of.options.other.reset=Réinitialiser les réglages vidéos...

of.shaders.profile=Profil

# Quality

of.options.mipmap.bilinear=Bilinéaire
of.options.mipmap.linear=Linéaire
of.options.mipmap.nearest=Le plus proche
of.options.mipmap.trilinear=Trilinéaire

options.mipmapLevels.tooltip.1=Effet visuel qui rend les objets lointains plus beaux
options.mipmapLevels.tooltip.2=en adoucissant les détails
options.mipmapLevels.tooltip.3=  Non - aucun adoucissement
options.mipmapLevels.tooltip.4=  1 - adoucissement minimum
options.mipmapLevels.tooltip.5=  4 - adoucissement maximum
options.mipmapLevels.tooltip.6=Ceci n'affecte généralement pas les performances.

of.options.MIPMAP_TYPE=Type de mipmap 
of.options.MIPMAP_TYPE.tooltip.1=Effet visuel qui rend les objets lointains plus beau
of.options.MIPMAP_TYPE.tooltip.2=en adoucissant les détails
of.options.MIPMAP_TYPE.tooltip.3=  Rapproché - adoucissement esquissé (rapide)
of.options.MIPMAP_TYPE.tooltip.4=  Linéaire - adoucissement normal
of.options.MIPMAP_TYPE.tooltip.5=  Bilinéaire - adoucissement travaillé
of.options.MIPMAP_TYPE.tooltip.6=  Trilinéaire - adoucissement magnifique (lent)


of.options.AA_LEVEL=Anticrénelage 
of.options.AA_LEVEL.tooltip.1=Anticrénelage
of.options.AA_LEVEL.tooltip.2=  Non - (par défaut) sans anticrénelage (rapide)
of.options.AA_LEVEL.tooltip.3=  2-16 - anticrénelage des lignes et bords (lent)
of.options.AA_LEVEL.tooltip.4=L'anticrénelage adoucit les lignes rugueuses et les
of.options.AA_LEVEL.tooltip.5=transitions rapides de couleurs.
of.options.AA_LEVEL.tooltip.6=Les FPS peuvent fortement chuter lorsque cette option est activée.
of.options.AA_LEVEL.tooltip.7=Tous les niveaux ne sont pas supportés par toutes les cartes graphiques.
of.options.AA_LEVEL.tooltip.8=Ne prend effet qu'après un REDÉMARRAGE !

of.options.AF_LEVEL=Filtrage anisotrope 
of.options.AF_LEVEL.tooltip.1=Filtrage anisotrope
of.options.AF_LEVEL.tooltip.2=  Non - (par défaut) détail de texture standard (rapide)
of.options.AF_LEVEL.tooltip.3=  2-16 - détails plus fins dans les textures mipmappées (lent)
of.options.AF_LEVEL.tooltip.4=Le filtrage anisotrope rétablit les détails dans les
of.options.AF_LEVEL.tooltip.5=textures mipmappées.
of.options.AF_LEVEL.tooltip.6=Les FPS peuvent fortement chuter lorsque cette option est
of.options.AF_LEVEL.tooltip.7=activée.

of.options.CLEAR_WATER=Eau claire 
of.options.CLEAR_WATER.tooltip.1=Eau claire
of.options.CLEAR_WATER.tooltip.2=  Oui - eau transparente, claire
of.options.CLEAR_WATER.tooltip.3=  Non - eau par défaut

of.options.RANDOM_ENTITIES=Entités aléatoires 
of.options.RANDOM_ENTITIES.tooltip.1=Entités aléatoires
of.options.RANDOM_ENTITIES.tooltip.2=  Non - aucune entité aléatoire (rapide)
of.options.RANDOM_ENTITIES.tooltip.3=  Oui - entités aléatoires activées (lent)
of.options.RANDOM_ENTITIES.tooltip.4=Utilise des textures aléatoires pour les entités du jeu.
of.options.RANDOM_ENTITIES.tooltip.5=Nécessite un pack de ressources avec plusieurs
of.options.RANDOM_ENTITIES.tooltip.6=textures par entité.

of.options.BETTER_GRASS=Meilleurs blocs d'herbe 
of.options.BETTER_GRASS.tooltip.1=Meilleurs blocs d'herbe
of.options.BETTER_GRASS.tooltip.2=  Non - texture par défaut (rapide)
of.options.BETTER_GRASS.tooltip.3=  Rapides - bords de bloc d'herbe complet (lent)
of.options.BETTER_GRASS.tooltip.4=  Détaillés - bords de bloc d'herbe détaillés (très lent)

of.options.BETTER_SNOW=Meilleure neige 
of.options.BETTER_SNOW.tooltip.1=Meilleure neige
of.options.BETTER_SNOW.tooltip.2=  Non - Neige par défaut (rapide)
of.options.BETTER_SNOW.tooltip.3=  Oui - Meilleure neige (lent)
of.options.BETTER_SNOW.tooltip.4=Fait apparaître de la neige sous des blocs transparents
of.options.BETTER_SNOW.tooltip.5=(barrière, hautes herbes) lorsqu'ils sont entourés de
of.options.BETTER_SNOW.tooltip.6=neige.

of.options.CUSTOM_FONTS=Police personnalisée 
of.options.CUSTOM_FONTS.tooltip.1=Police personnalisée
of.options.CUSTOM_FONTS.tooltip.2=  Oui - utilise la police personnalisée (par défaut, lent)
of.options.CUSTOM_FONTS.tooltip.3=  Non - utilise la police par défaut (rapide)
of.options.CUSTOM_FONTS.tooltip.4=La police personnalisée est fournie par le pack de
of.options.CUSTOM_FONTS.tooltip.5=ressources utilisé.

of.options.CUSTOM_COLORS=Couleurs personnalisées 
of.options.CUSTOM_COLORS.tooltip.1=Couleurs personnalisées
of.options.CUSTOM_COLORS.tooltip.2=  Oui - utilise des couleurs personnalisées (par défaut, lent)
of.options.CUSTOM_COLORS.tooltip.3=  Non - utilise les couleurs par défaut (rapide)
of.options.CUSTOM_COLORS.tooltip.4=Les couleurs personnalisées sont fournies par le pack
of.options.CUSTOM_COLORS.tooltip.5=de ressources utilisé.

of.options.SWAMP_COLORS=Couleurs marécageuses 
of.options.SWAMP_COLORS.tooltip.1=Couleurs marécageuses
of.options.SWAMP_COLORS.tooltip.2=  Oui - utilise des couleurs marécageuses (par défaut, lent)
of.options.SWAMP_COLORS.tooltip.3=  Non - n'utilise pas de couleurs marécageuses (rapide)
of.options.SWAMP_COLORS.tooltip.4=Les couleurs marécageuses modifient la pelouse, les
of.options.SWAMP_COLORS.tooltip.5=feuilles, les lianes et l'eau.

of.options.SMOOTH_BIOMES=Biomes adoucis 
of.options.SMOOTH_BIOMES.tooltip.1=Biomes adoucis
of.options.SMOOTH_BIOMES.tooltip.2=  Oui - adoucit la bordure des biomes (par défaut, lent)
of.options.SMOOTH_BIOMES.tooltip.3=  Non - n'adoucit pas la bordure des biomes (rapide)
of.options.SMOOTH_BIOMES.tooltip.4=L'adoucissement des bordures est fait en échantillonnant et
of.options.SMOOTH_BIOMES.tooltip.5=moyennant la couleur des blocs aux frontières de biomes.
of.options.SMOOTH_BIOMES.tooltip.6=N'affecte que la pelouse, les feuilles, les lianes et l'eau.

of.options.CONNECTED_TEXTURES=Textures connectées 
of.options.CONNECTED_TEXTURES.tooltip.1=Textures connectées
of.options.CONNECTED_TEXTURES.tooltip.2=  Non - aucune connexion (par défaut)
of.options.CONNECTED_TEXTURES.tooltip.3=  Rapides - connexions rapides
of.options.CONNECTED_TEXTURES.tooltip.4=  Détaillés - connexions détaillées
of.options.CONNECTED_TEXTURES.tooltip.5=Les textures connectées connecte les textures des
of.options.CONNECTED_TEXTURES.tooltip.6=vitres, du grès et des bibliothèques placés côte-à-côte.
of.options.CONNECTED_TEXTURES.tooltip.7=Les textures connectées sont fournies par le pack de
of.options.CONNECTED_TEXTURES.tooltip.8=ressources utilisé.

of.options.NATURAL_TEXTURES=Textures naturelles 
of.options.NATURAL_TEXTURES.tooltip.1=Textures naturelles
of.options.NATURAL_TEXTURES.tooltip.2=  Non - sans textures naturelles (par défaut)
of.options.NATURAL_TEXTURES.tooltip.3=  Oui - avec textures naturelles
of.options.NATURAL_TEXTURES.tooltip.4=Les textures naturelles enlèvent le motif grillagé créé
of.options.NATURAL_TEXTURES.tooltip.5=par la répétition de blocs identiques, en utilisant des
of.options.NATURAL_TEXTURES.tooltip.6=variations pivotées et retournées de la texture du bloc.
of.options.NATURAL_TEXTURES.tooltip.7=La configuration pour les textures naturelles est fournie
of.options.NATURAL_TEXTURES.tooltip.8=par le pack de ressources utilisé.

of.options.EMISSIVE_TEXTURES=Textures émissives 
of.options.EMISSIVE_TEXTURES.tooltip.1=Textures émissives
of.options.EMISSIVE_TEXTURES.tooltip.2=  Non - pas de textures émissives (par défaut)
of.options.EMISSIVE_TEXTURES.tooltip.3=  Oui - utilise des textures émissives
of.options.EMISSIVE_TEXTURES.tooltip.4=Les textures émissives sont rendues en tant que surcouche
of.options.EMISSIVE_TEXTURES.tooltip.5=avec luminosité maximale. Elles peuvent être utilisées pour
of.options.EMISSIVE_TEXTURES.tooltip.6=simuler des parties lumineuses dans les textures de base.
of.options.EMISSIVE_TEXTURES.tooltip.7=Les textures émissives sont fournies par le pack de
of.options.EMISSIVE_TEXTURES.tooltip.8=ressources utilisé.

of.options.CUSTOM_SKY=Ciel personnalisé 
of.options.CUSTOM_SKY.tooltip.1=Ciel personnalisé
of.options.CUSTOM_SKY.tooltip.2=  Oui - texture de ciel personnalisée (par défaut, lent)
of.options.CUSTOM_SKY.tooltip.3=  Non - ciel par défaut (rapide)
of.options.CUSTOM_SKY.tooltip.4=Les textures de ciel personnalisé sont fournies par le
of.options.CUSTOM_SKY.tooltip.5=pack de ressources utilisé.

of.options.CUSTOM_ITEMS=Objets personnalisés 
of.options.CUSTOM_ITEMS.tooltip.1=Objets personnalisés
of.options.CUSTOM_ITEMS.tooltip.2=  Oui - textures d'objets personnalisés (par défaut, lent)
of.options.CUSTOM_ITEMS.tooltip.3=  Non - textures d'objets par défaut (rapide)
of.options.CUSTOM_ITEMS.tooltip.4=Les textures d'objets personnalisés sont fournis par
of.options.CUSTOM_ITEMS.tooltip.5=le pack de ressources utilisé.

of.options.CUSTOM_ENTITY_MODELS=Modèles d'entités perso. 
of.options.CUSTOM_ENTITY_MODELS.tooltip.1=Modèles d'entités personnalisées
of.options.CUSTOM_ENTITY_MODELS.tooltip.2=  Oui - modèles d'entités personnalisées (par défaut, lent)
of.options.CUSTOM_ENTITY_MODELS.tooltip.3=  Non - modèles d'entités par défaut (rapide)
of.options.CUSTOM_ENTITY_MODELS.tooltip.4=Les modèles d'entités personnalisées sont fournis par le
of.options.CUSTOM_ENTITY_MODELS.tooltip.5=pack de ressources utilisé.

of.options.CUSTOM_GUIS=Interfaces perso. 
of.options.CUSTOM_GUIS.tooltip.1=Interfaces personnalisées
of.options.CUSTOM_GUIS.tooltip.2=  Oui - interfaces personnalisées (par défaut, lent)
of.options.CUSTOM_GUIS.tooltip.3=  Non - interfaces par défaut (rapide)
of.options.CUSTOM_GUIS.tooltip.4=Les interfaces personnalisées sont fournies par le pack
of.options.CUSTOM_GUIS.tooltip.5=de ressources utilisé.

# Details

of.options.CLOUDS=Nuages 
of.options.CLOUDS.tooltip.1=Nuages
of.options.CLOUDS.tooltip.2=  Par défaut - comme dans les options graphiques
of.options.CLOUDS.tooltip.3=  Rapides - basse qualité (rapide)
of.options.CLOUDS.tooltip.4=  Détaillés - haute qualité (lent)
of.options.CLOUDS.tooltip.5=  Non - sans nuages (plus rapide)
of.options.CLOUDS.tooltip.6=Les nuages rapides sont rendus en 2D.
of.options.CLOUDS.tooltip.7=Les nuages détaillés sont rendus en 3D.

of.options.CLOUD_HEIGHT=Hauteur des nuages 
of.options.CLOUD_HEIGHT.tooltip.1=Hauteur des nuages
of.options.CLOUD_HEIGHT.tooltip.2=  Non - hauteur par défaut
of.options.CLOUD_HEIGHT.tooltip.3=  100 %% - au-dessus de la limite de hauteur du monde

of.options.TREES=Arbres 
of.options.TREES.tooltip.1=Arbres
of.options.TREES.tooltip.2=  Par défaut - comme dans les options graphiques
of.options.TREES.tooltip.3=  Rapides - basse qualité (plus rapide)
of.options.TREES.tooltip.4=  Approfondis - haute qualité (rapide)
of.options.TREES.tooltip.5=  Détaillés - très haute qualité (lent)
of.options.TREES.tooltip.6=Les arbres rapides ont des feuilles opaques.
of.options.TREES.tooltip.7=Les arbres approfondis et détaillés ont des feuilles
of.options.TREES.tooltip.8=transparentes.

of.options.RAIN=Pluie & neige 
of.options.RAIN.tooltip.1=Pluie & neige
of.options.RAIN.tooltip.2=  Par défaut - comme dans les options graphiques
of.options.RAIN.tooltip.3=  Rapide  - légère pluie/neige (rapide)
of.options.RAIN.tooltip.4=  Détaillés - grosse pluie/neige (lent)
of.options.RAIN.tooltip.5=  Non - sans pluie/neige (plus rapide)
of.options.RAIN.tooltip.6=Même si la pluie et la neige sont désactivées, les
of.options.RAIN.tooltip.7=éclaboussures et le son sont toujours présents.

of.options.SKY=Ciel 
of.options.SKY.tooltip.1=Ciel
of.options.SKY.tooltip.2=  Oui - le ciel est visible (lent)
of.options.SKY.tooltip.3=  Non - le ciel n'est pas visible (rapide)
of.options.SKY.tooltip.4=Quand le ciel est désactivé, le soleil et la lune sont
of.options.SKY.tooltip.5=toujours visibles.

of.options.STARS=Étoiles 
of.options.STARS.tooltip.1=Étoiles
of.options.STARS.tooltip.2=  Oui - les étoiles sont visibles (lent)
of.options.STARS.tooltip.3=  Non - les étoiles ne sont pas visibles (rapide)

of.options.SUN_MOON=Soleil & Lune 
of.options.SUN_MOON.tooltip.1=Soleil & Lune
of.options.SUN_MOON.tooltip.2=  Oui - le Soleil et la Lune sont visibles (par défaut)
of.options.SUN_MOON.tooltip.3=  Non - le Soleil et la Lune ne sont pas visibles (rapide)

of.options.SHOW_CAPES=Capes visibles 
of.options.SHOW_CAPES.tooltip.1=Capes visibles
of.options.SHOW_CAPES.tooltip.2=  Oui - voir la cape des joueurs (par défaut)
of.options.SHOW_CAPES.tooltip.3=  Non - ne pas voir la cape des joueurs

of.options.TRANSLUCENT_BLOCKS=Blocs translucides 
of.options.TRANSLUCENT_BLOCKS.tooltip.1=Blocs translucides
of.options.TRANSLUCENT_BLOCKS.tooltip.2=  Par défaut - comme défini par les graphismes
of.options.TRANSLUCENT_BLOCKS.tooltip.3=  Détaillés - mélange correct des couleurs (lent)
of.options.TRANSLUCENT_BLOCKS.tooltip.4=  Rapides - mélange rapide des couleurs (rapide)
of.options.TRANSLUCENT_BLOCKS.tooltip.5=Contrôle le mélange des couleurs des blocs translucides
of.options.TRANSLUCENT_BLOCKS.tooltip.6=avec différentes couleurs (verre teinté, eau, glace)
of.options.TRANSLUCENT_BLOCKS.tooltip.7=lorsque placé un à côté de l'autre avec de l'air entre eux.

of.options.HELD_ITEM_TOOLTIPS=Infobulles d'objets 
of.options.HELD_ITEM_TOOLTIPS.tooltip.1=Infobulles d'objets
of.options.HELD_ITEM_TOOLTIPS.tooltip.2=  Oui - affiche des infos sur l'objet en main (par défaut)
of.options.HELD_ITEM_TOOLTIPS.tooltip.3=  Non - n'affiche pas d'info sur l'objet en main

of.options.ADVANCED_TOOLTIPS=Infobulles avancées 
of.options.ADVANCED_TOOLTIPS.tooltip.1=Infobulles avancées
of.options.ADVANCED_TOOLTIPS.tooltip.2=  Oui - affiche les infobulles avancées
of.options.ADVANCED_TOOLTIPS.tooltip.3=  Non - n'affiche pas les infobulles avancées (par défaut)
of.options.ADVANCED_TOOLTIPS.tooltip.4=Les infobulles avancées affiche des informations détaillées
of.options.ADVANCED_TOOLTIPS.tooltip.5=sur l'objet (ID, durabilité) et les options des shaders
of.options.ADVANCED_TOOLTIPS.tooltip.6=(ID, source, valeur par défaut).

of.options.DROPPED_ITEMS=Objets lâchés 
of.options.DROPPED_ITEMS.tooltip.1=Objets lâchés
of.options.DROPPED_ITEMS.tooltip.2=  Par défaut - comme dans les options graphiques
of.options.DROPPED_ITEMS.tooltip.3=  Rapides - objets 2D (rapide)
of.options.DROPPED_ITEMS.tooltip.4=  Détaillés - objets 3D (lent)

options.entityShadows.tooltip.1=Ombres des entités 
options.entityShadows.tooltip.2=  Oui - affiche l'ombre des entités
options.entityShadows.tooltip.3=  Non - n'affiche pas l'ombre des entités

of.options.VIGNETTE=Vignette 
of.options.VIGNETTE.tooltip.1=Effet visuel qui assombrit les coins de l'écran
of.options.VIGNETTE.tooltip.2=  Par défaut - comme dans les options graphiques
of.options.VIGNETTE.tooltip.3=  Rapides - vignette désactivée (rapide)
of.options.VIGNETTE.tooltip.4=  Détaillés - vignette activée (lent)
of.options.VIGNETTE.tooltip.5=La vignette peut avoir un effet significatif sur les FPS,
of.options.VIGNETTE.tooltip.6=notamment lorsque le jeu est en plein écran.
of.options.VIGNETTE.tooltip.7=L'effet de vignette est très subtil et peut être désactivé
of.options.VIGNETTE.tooltip.8=en toute sécurité.

of.options.DYNAMIC_FOV=Champ visuel dynamique 
of.options.DYNAMIC_FOV.tooltip.1=Champ de vision dynamique
of.options.DYNAMIC_FOV.tooltip.2=  Oui - activer le champ de vision dynamique (par défaut)
of.options.DYNAMIC_FOV.tooltip.3=  Non - désactiver le champ de vision dynamique
of.options.DYNAMIC_FOV.tooltip.4=Change le champ de vision lors du vol, du sprint, de la
of.options.DYNAMIC_FOV.tooltip.5=nage et du bandage d'un arc.

of.options.DYNAMIC_LIGHTS=Lumières dynamiques 
of.options.DYNAMIC_LIGHTS.tooltip.1=Lumières dynamiques
of.options.DYNAMIC_LIGHTS.tooltip.2=  Non - aucune lumière dynamique (par défaut)
of.options.DYNAMIC_LIGHTS.tooltip.3=  Rapides - lumières dynamiques rafraîchies tous les 500 ms
of.options.DYNAMIC_LIGHTS.tooltip.4=  Détaillés - Lumières dynamiques rafraîchies en temps réel
of.options.DYNAMIC_LIGHTS.tooltip.5=Permet aux objets émetteurs de lumière (torche, pierre
of.options.DYNAMIC_LIGHTS.tooltip.6=lumineuse, etc.) d'illuminer tout autour du joueur quand
of.options.DYNAMIC_LIGHTS.tooltip.7=l'objet est en main, équipé par d'autres joueurs ou jeté
of.options.DYNAMIC_LIGHTS.tooltip.8=au sol.

options.biomeBlendRadius.tooltip.1=Adoucit la transition de couleur entre les biomes
options.biomeBlendRadius.tooltip.2=  Non - pas de transition (rapide)
options.biomeBlendRadius.tooltip.3=  5x5 - transition normale (par défaut)
options.biomeBlendRadius.tooltip.4=  15x15 - transition maximale (lent)
options.biomeBlendRadius.tooltip.5=Des valeurs hautes peuvent causer d'importants pics de
options.biomeBlendRadius.tooltip.6=lag et ralentir la vitesse de chargement des tronçons.

# Performance

of.options.SMOOTH_FPS=FPS atténué 
of.options.SMOOTH_FPS.tooltip.1=Stabilise les FPS en supprimant les tampons de trame.
of.options.SMOOTH_FPS.tooltip.2=  Non - aucune stabilisation, les FPS peuvent varier
of.options.SMOOTH_FPS.tooltip.3=  Oui - stabilisation des FPS
of.options.SMOOTH_FPS.tooltip.4=Cette option dépend du pilote graphique, les effets ne
of.options.SMOOTH_FPS.tooltip.5=sont pas toujours visibles.

of.options.SMOOTH_WORLD=Monde atténué 
of.options.SMOOTH_WORLD.tooltip.1=Élimine les pics de lag causés par le serveur interne.
of.options.SMOOTH_WORLD.tooltip.2=  Non - aucune stabilisation, les FPS peuvent varier
of.options.SMOOTH_WORLD.tooltip.3=  Oui - stabilisation des FPS
of.options.SMOOTH_WORLD.tooltip.4=Stabilise les FPS en distribuant les charges du serveur
of.options.SMOOTH_WORLD.tooltip.5=interne. Fonctionne uniquement en local (monde solo).

of.options.FAST_RENDER=Rendu rapide 
of.options.FAST_RENDER.tooltip.1=Rendu rapide
of.options.FAST_RENDER.tooltip.2=  Non - rendu standard (par défaut)
of.options.FAST_RENDER.tooltip.3=  Oui - rendu optimisé (rapide)
of.options.FAST_RENDER.tooltip.4=Utilise des algorithmes de rendu optimisé pour diminuer la
of.options.FAST_RENDER.tooltip.5=charge du processeur graphique, ce qui peut augmenter
of.options.FAST_RENDER.tooltip.6=les FPS.
of.options.FAST_RENDER.tooltip.7=Cette option peut entrer en conflit avec certains mods.

of.options.FAST_MATH=Calculs rapides 
of.options.FAST_MATH.tooltip.1=Calculs rapides
of.options.FAST_MATH.tooltip.2=  Non - calculs standards (par défaut)
of.options.FAST_MATH.tooltip.3=  Oui - calculs optimisés
of.options.FAST_MATH.tooltip.4=Utilise des fonctions optimisées de sin() et cos() qui
of.options.FAST_MATH.tooltip.5=peuvent mieux utiliser le cache du processeur et augmenter
of.options.FAST_MATH.tooltip.6=les FPS.
of.options.FAST_MATH.tooltip.7=Cette option peut avoir un léger impact sur la génération
of.options.FAST_MATH.tooltip.8=du monde.

of.options.CHUNK_UPDATES=Mises à jour des tronçons 
of.options.CHUNK_UPDATES.tooltip.1=Mises à jour des tronçons
of.options.CHUNK_UPDATES.tooltip.2=  1 - chargement lent du monde, meilleur FPS (par défaut)
of.options.CHUNK_UPDATES.tooltip.3=  3 - chargement rapide du monde, bas FPS
of.options.CHUNK_UPDATES.tooltip.4=  5 - chargement très rapide du monde, très bas FPS
of.options.CHUNK_UPDATES.tooltip.5=Nombre de mises à jour de tronçons par image rendue
of.options.CHUNK_UPDATES.tooltip.6=(tick), les valeurs élevées peuvent déstabiliser les FPS.

of.options.CHUNK_UPDATES_DYNAMIC=Mises à jour dynamiques 
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.1=Mises à jours dynamiques
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.2=  Non - nombre de mises à jour stantard (par défaut)
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.3=  Oui - plus de mises à jour lorsque le joueur est inactif
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.4=Les mises à jour dynamiques forcent plus de tronçons à
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.5=se mettre à jour quand le joueur est inactif, pour
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.6=charger le monde plus vite.

of.options.LAZY_CHUNK_LOADING=Charg. de tronçons lent 
of.options.LAZY_CHUNK_LOADING.tooltip.1=Chargement lent des tronçons
of.options.LAZY_CHUNK_LOADING.tooltip.2=  Non - chargement des tronçons serveurs par défaut
of.options.LAZY_CHUNK_LOADING.tooltip.3=  Oui - chargement lent des tronçons serveur (plus doux)
of.options.LAZY_CHUNK_LOADING.tooltip.4=Adoucit le chargement des tronçons serveurs intégrés
of.options.LAZY_CHUNK_LOADING.tooltip.5=en distribuant les tronçons sur plusieurs ticks.
of.options.LAZY_CHUNK_LOADING.tooltip.6=Désactivez-le si des parties du monde ne se chargent
of.options.LAZY_CHUNK_LOADING.tooltip.7=pas correctement. Fonctionne uniquement en local (solo).

of.options.RENDER_REGIONS=Rendu des régions 
of.options.RENDER_REGIONS.tooltip.1=Rendu des régions
of.options.RENDER_REGIONS.tooltip.2= Non - rendu standard (par défaut)
of.options.RENDER_REGIONS.tooltip.3= Oui - rendu des régions
of.options.RENDER_REGIONS.tooltip.4=Permet un rendu du terrain plus rapide à des distances
of.options.RENDER_REGIONS.tooltip.5=d'affichage plus élevées. Plus efficace lorsque les VBOs
of.options.RENDER_REGIONS.tooltip.6=sont activés. Non recommandé pour les cartes graphiques
of.options.RENDER_REGIONS.tooltip.7=intégrées.

of.options.SMART_ANIMATIONS=Animations intelligentes 
of.options.SMART_ANIMATIONS.tooltip.1=Animations intelligentes
of.options.SMART_ANIMATIONS.tooltip.2= Non - animations standards (par défaut)
of.options.SMART_ANIMATIONS.tooltip.3= Oui - animations intelligentes
of.options.SMART_ANIMATIONS.tooltip.4=Permet au jeu d'animer uniquement les textures
of.options.SMART_ANIMATIONS.tooltip.5=actuellement visibles à l'écran. Cela réduit les pics
of.options.SMART_ANIMATIONS.tooltip.6=de lag et augmente les FPS. Surtout utile pour les gros
of.options.SMART_ANIMATIONS.tooltip.7=packs de mods et les packs de ressources HD.

# Animations

of.options.animation.allOn=Activer tout
of.options.animation.allOff=Désact. tout
of.options.animation.dynamic=Dynamique

of.options.ANIMATED_WATER=Animation de l'eau 
of.options.ANIMATED_LAVA=Animation de la lave 
of.options.ANIMATED_FIRE=Animation du feu 
of.options.ANIMATED_PORTAL=Animation des portails 
of.options.ANIMATED_REDSTONE=Animation de la redstone 
of.options.ANIMATED_EXPLOSION=Animation des explosions 
of.options.ANIMATED_FLAME=Animation des flammes 
of.options.ANIMATED_SMOKE=Animation de fumées 
of.options.VOID_PARTICLES=Particules de vide 
of.options.WATER_PARTICLES=Particules d'eau 
of.options.RAIN_SPLASH=Particules de pluie 
of.options.PORTAL_PARTICLES=Particules de portail 
of.options.POTION_PARTICLES=Particules de potion 
of.options.DRIPPING_WATER_LAVA=Gouttes d'eau/lave 
of.options.ANIMATED_TERRAIN=Animation du terrain 
of.options.ANIMATED_TEXTURES=Animation des textures 
of.options.FIREWORK_PARTICLES=Partic. de feux d'artifice 

# Other

of.options.LAGOMETER=Lagomètre 
of.options.LAGOMETER.tooltip.1=Affiche le lagomètre sur l'écran de débogage (F3).
of.options.LAGOMETER.tooltip.2=* Orange - Récupérateur de mémoire
of.options.LAGOMETER.tooltip.3=* Cyan - Tick
of.options.LAGOMETER.tooltip.4=* Bleu - Exécutables programmés
of.options.LAGOMETER.tooltip.5=* Violet - Téléchargement de tronçons
of.options.LAGOMETER.tooltip.6=* Rouge - Mises à jour de tronçon
of.options.LAGOMETER.tooltip.7=* Jaune - Contrôle de visibilité
of.options.LAGOMETER.tooltip.8=* Vert - Terrain rendu

of.options.PROFILER=Profileur de débogage 
of.options.PROFILER.tooltip.1=Profileur de débogage
of.options.PROFILER.tooltip.2=  Oui - profileur actif (lent)
of.options.PROFILER.tooltip.3=  Non - profileur non actif (rapide)
of.options.PROFILER.tooltip.4=Le profileur de débogage collecte et affiche des
of.options.PROFILER.tooltip.5=informations de débogage lorsque l'écran de débogage
of.options.PROFILER.tooltip.6=(F3) est ouvert.

of.options.WEATHER=Météo 
of.options.WEATHER.tooltip.1=Météo
of.options.WEATHER.tooltip.2=  Oui - Météo active (lent)
of.options.WEATHER.tooltip.3=  Non - Météo non active (rapide)
of.options.WEATHER.tooltip.4=La météo contrôle la pluie, la neige et l'orage.
of.options.WEATHER.tooltip.5=Le contrôle de la météo n'est possible qu'en local.

of.options.time.dayOnly=Jour seulement
of.options.time.nightOnly=Nuit seulement

of.options.TIME=Temps 
of.options.TIME.tooltip.1=Temps
of.options.TIME.tooltip.2=  Par défaut - cycle jour/nuit normal
of.options.TIME.tooltip.3=  Jour seulement - jour continu
of.options.TIME.tooltip.4=  Nuit seulement - nuit continue
of.options.TIME.tooltip.5=La sélection du temps n'est possible qu'en MODE CRÉATIF
of.options.TIME.tooltip.6=dans un monde local.

options.fullscreen.tooltip.1=Plein écran
options.fullscreen.tooltip.2=  Oui - jeu en plein écran
options.fullscreen.tooltip.3=  Non - jeu en mode fenêtré
options.fullscreen.tooltip.4=Le mode plein écran peut être plus rapide ou plus lent
options.fullscreen.tooltip.5=que le mode fenêtré selon la carte graphique.

options.fullscreen.resolution=Mode plein écran 
options.fullscreen.resolution.tooltip.1=Mode plein écran
options.fullscreen.resolution.tooltip.2=  Par défaut - utilise la résolution du moniteur (lent)
options.fullscreen.resolution.tooltip.3=  LxH - utilise une résolution personnalisée (parfois rapide)
options.fullscreen.resolution.tooltip.4=La résolution sélectionnée est utilisée en mode plein écran (F11).
options.fullscreen.resolution.tooltip.5=Une résolution plus basse devrait être plus rapide.

of.options.SHOW_FPS=Afficher les FPS 
of.options.SHOW_FPS.tooltip.1=Affiche les FPS et informations de rendu.
of.options.SHOW_FPS.tooltip.2=  FPS - moyenne/minimum
of.options.SHOW_FPS.tooltip.3=  C: - tronçons rendus
of.options.SHOW_FPS.tooltip.4=  E: - entités et entités de blocs rendues
of.options.SHOW_FPS.tooltip.5=  U: - mises à jour de tronçon
of.options.SHOW_FPS.tooltip.6=Les informations sont présentées dans le coin supérieur
of.options.SHOW_FPS.tooltip.7=gauche lorsque l'écran de débogage n'est pas visible.

of.options.save.45s=45s
of.options.save.90s=90s
of.options.save.3min=3min
of.options.save.6min=6min
of.options.save.12min=12min
of.options.save.24min=24min

of.options.AUTOSAVE_TICKS=Sauvegarde auto. 
of.options.AUTOSAVE_TICKS.tooltip.1=Intervalle entre les sauvegardes automatiques
of.options.AUTOSAVE_TICKS.tooltip.2= 45s - par défaut
of.options.AUTOSAVE_TICKS.tooltip.3=Les sauvegardes automatiques peuvent causer des pics
of.options.AUTOSAVE_TICKS.tooltip.4=de lag en fonction de la distance d'affichage. Le monde est
of.options.AUTOSAVE_TICKS.tooltip.5=aussi sauvegardé lorsque le menu du jeu est ouvert.

of.options.SCREENSHOT_SIZE=Captures d'écran 
of.options.SCREENSHOT_SIZE.tooltip.1=Taille des captures d'écran
of.options.SCREENSHOT_SIZE.tooltip.2=  Par défaut - taille de la fenêtre
of.options.SCREENSHOT_SIZE.tooltip.3=  2x-4x - taille personnalisée
of.options.SCREENSHOT_SIZE.tooltip.4=Prendre une capture d'écran plus grande peut
of.options.SCREENSHOT_SIZE.tooltip.5=nécessiter plus de mémoire.
of.options.SCREENSHOT_SIZE.tooltip.6=Incompatible avec le rendu rapide et l'anticrénelage.
of.options.SCREENSHOT_SIZE.tooltip.7=Requiert un processeur graphique avec un tampon de trame.

of.options.SHOW_GL_ERRORS=Afficher les erreurs GL 
of.options.SHOW_GL_ERRORS.tooltip.1=Afficher les erreurs OpenGL
of.options.SHOW_GL_ERRORS.tooltip.2=Les erreurs OpenGL sont affichées dans le tchat lorsque
of.options.SHOW_GL_ERRORS.tooltip.3=cette option est activée. Ne la désactiver que s'il y a un
of.options.SHOW_GL_ERRORS.tooltip.4=conflit inconnu et que les erreurs ne peuvent être
of.options.SHOW_GL_ERRORS.tooltip.5=corrigées. Les erreurs sont toujours journalisées lorsque
of.options.SHOW_GL_ERRORS.tooltip.6=cette option est désactivée et peuvent toujours causer
of.options.SHOW_GL_ERRORS.tooltip.7=une chute importante de FPS.

# Chat Settings

of.options.CHAT_BACKGROUND=Fond du tchat 
of.options.CHAT_BACKGROUND.tooltip.1=Fond du tchat
of.options.CHAT_BACKGROUND.tooltip.2=  Par défaut - largeur fixe
of.options.CHAT_BACKGROUND.tooltip.3=  Compact - s'adapte à la largeur des lignes
of.options.CHAT_BACKGROUND.tooltip.4=  Non - masqué

of.options.CHAT_SHADOW=Ombre du tchat 
of.options.CHAT_SHADOW.tooltip.1=Ombre du tchat
of.options.CHAT_SHADOW.tooltip.2=  Oui - ombre du tchat (par défaut)
of.options.CHAT_SHADOW.tooltip.3=  Non - pas d'ombre du tchat
