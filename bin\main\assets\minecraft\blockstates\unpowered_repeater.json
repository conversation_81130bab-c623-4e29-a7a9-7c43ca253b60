{"variants": {"delay=1,facing=south,locked=false": {"model": "repeater_1tick"}, "delay=1,facing=west,locked=false": {"model": "repeater_1tick", "y": 90}, "delay=1,facing=north,locked=false": {"model": "repeater_1tick", "y": 180}, "delay=1,facing=east,locked=false": {"model": "repeater_1tick", "y": 270}, "delay=2,facing=south,locked=false": {"model": "repeater_2tick"}, "delay=2,facing=west,locked=false": {"model": "repeater_2tick", "y": 90}, "delay=2,facing=north,locked=false": {"model": "repeater_2tick", "y": 180}, "delay=2,facing=east,locked=false": {"model": "repeater_2tick", "y": 270}, "delay=3,facing=south,locked=false": {"model": "repeater_3tick"}, "delay=3,facing=west,locked=false": {"model": "repeater_3tick", "y": 90}, "delay=3,facing=north,locked=false": {"model": "repeater_3tick", "y": 180}, "delay=3,facing=east,locked=false": {"model": "repeater_3tick", "y": 270}, "delay=4,facing=south,locked=false": {"model": "repeater_4tick"}, "delay=4,facing=west,locked=false": {"model": "repeater_4tick", "y": 90}, "delay=4,facing=north,locked=false": {"model": "repeater_4tick", "y": 180}, "delay=4,facing=east,locked=false": {"model": "repeater_4tick", "y": 270}, "delay=1,facing=south,locked=true": {"model": "repeater_locked_1tick"}, "delay=1,facing=west,locked=true": {"model": "repeater_locked_1tick", "y": 90}, "delay=1,facing=north,locked=true": {"model": "repeater_locked_1tick", "y": 180}, "delay=1,facing=east,locked=true": {"model": "repeater_locked_1tick", "y": 270}, "delay=2,facing=south,locked=true": {"model": "repeater_locked_2tick"}, "delay=2,facing=west,locked=true": {"model": "repeater_locked_2tick", "y": 90}, "delay=2,facing=north,locked=true": {"model": "repeater_locked_2tick", "y": 180}, "delay=2,facing=east,locked=true": {"model": "repeater_locked_2tick", "y": 270}, "delay=3,facing=south,locked=true": {"model": "repeater_locked_3tick"}, "delay=3,facing=west,locked=true": {"model": "repeater_locked_3tick", "y": 90}, "delay=3,facing=north,locked=true": {"model": "repeater_locked_3tick", "y": 180}, "delay=3,facing=east,locked=true": {"model": "repeater_locked_3tick", "y": 270}, "delay=4,facing=south,locked=true": {"model": "repeater_locked_4tick"}, "delay=4,facing=west,locked=true": {"model": "repeater_locked_4tick", "y": 90}, "delay=4,facing=north,locked=true": {"model": "repeater_locked_4tick", "y": 180}, "delay=4,facing=east,locked=true": {"model": "repeater_locked_4tick", "y": 270}}}