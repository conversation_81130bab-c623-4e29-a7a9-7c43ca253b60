# Contributors of British English (upside down) #
# DaDodger and sp614x for creating en_gb.lang
# Regnander for converting and proofreading the result

# General
of.general.ambiguous=snonᵷᴉqɯɐ
of.general.compact=ʇɔɐdɯoƆ
of.general.custom=ɯoʇsnƆ
of.general.from=ɯoɹℲ
of.general.id=pI
of.general.max=ɯnɯᴉxɐW
of.general.restart=ʇɹɐʇsǝɹ
of.general.smart=ʇɹɐɯS

# Keys
of.key.zoom=ɯooZ

# Message
of.message.aa.shaders2=˙sɹǝpɐɥS ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ ʇou sᴉ ᵷuᴉsɐᴉꞁɐᴉʇuⱯ
of.message.aa.shaders1=˙uoᴉʇdo sᴉɥʇ ǝꞁqɐuǝ oʇ sɹǝpɐɥS ǝꞁqɐsᴉp ǝsɐǝꞁԀ

of.message.af.shaders2=˙sɹǝpɐɥS ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ ʇou sᴉ ᵷuᴉɹǝʇꞁᴉℲ ɔᴉdoɹʇosᴉuⱯ
of.message.af.shaders1=˙uoᴉʇdo sᴉɥʇ ǝꞁqɐuǝ oʇ sɹǝpɐɥS ǝꞁqɐsᴉp ǝsɐǝꞁԀ

of.message.fr.shaders2=˙sɹǝpɐɥS ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ ʇou sᴉ ɹǝpuǝᴚ ʇsɐℲ
of.message.fr.shaders1=˙uoᴉʇdo sᴉɥʇ ǝꞁqɐuǝ oʇ sɹǝpɐɥS ǝꞁqɐsᴉp ǝsɐǝꞁԀ

of.message.an.shaders2=˙sɹǝpɐɥS ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ ʇou sᴉ ɥdʎꞁᵷɐuⱯ ᗡƐ
of.message.an.shaders1=˙uoᴉʇdo sᴉɥʇ ǝꞁqɐuǝ oʇ sɹǝpɐɥS ǝꞁqɐsᴉp ǝsɐǝꞁԀ

of.message.shaders.aa2=˙ᵷuᴉsɐᴉꞁɐᴉʇuⱯ ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ ʇou ǝɹɐ sɹǝpɐɥS
of.message.shaders.aa1=˙ǝɯɐᵷ ǝɥʇ ʇɹɐʇsǝɹ puɐ ℲℲO oʇ ᵷuᴉsɐᴉꞁɐᴉʇuⱯ <- ʎʇᴉꞁɐnꝹ ʇǝs ǝsɐǝꞁԀ

of.message.shaders.af2=˙ᵷuᴉɹǝʇꞁᴉℲ ɔᴉdoɹʇosᴉuⱯ ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ ʇou ǝɹɐ sɹǝpɐɥS
of.message.shaders.af1=˙ℲℲO oʇ ᵷuᴉɹǝʇꞁᴉℲ ɔᴉdoɹʇosᴉuⱯ <- ʎʇᴉꞁɐnꝹ ʇǝs ǝsɐǝꞁԀ

of.message.shaders.fr2=˙ɹǝpuǝᴚ ʇsɐℲ ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ ʇou ǝɹɐ sɹǝpɐɥS
of.message.shaders.fr1=˙ℲℲO oʇ ɹǝpuǝᴚ ʇsɐℲ <- ǝɔuɐɯɹoɟɹǝԀ ʇǝs ǝsɐǝꞁԀ

of.message.shaders.gf2=˙sɔᴉɥdɐɹ⅁ snoꞁnqɐℲ ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ ʇou ǝɹɐ sɹǝpɐɥS
of.message.shaders.gf1=˙ʎɔuɐℲ ɹo ʇsɐℲ oʇ sɔᴉɥdɐɹ⅁ ʇǝs ǝsɐǝꞁԀ

of.message.shaders.an2=˙ɥdʎꞁᵷɐuⱯ ᗡƐ ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ ʇou ǝɹɐ sɹǝpɐɥS
of.message.shaders.an1=˙ℲℲO oʇ ɥdʎꞁᵷɐuⱯ ᗡƐ <- ɹǝɥʇO ʇǝs ǝsɐǝꞁԀ

of.message.shaders.nv2=%s :uoᴉsɹǝʌ ǝuᴉℲᴉʇdO ɹǝʍǝu ɐ sǝɹᴉnbǝɹ ʞɔɐd ɹǝpɐɥs sᴉɥ⟘
of.message.shaders.nv1=¿ǝnuᴉʇuoɔ oʇ ʇuɐʍ noʎ ǝɹns noʎ ǝɹⱯ

of.message.newVersion=§f§e%s§f :ǝꞁqɐꞁᴉɐʌɐ sᴉ uoᴉsɹǝʌ §eǝuᴉℲᴉʇdO§r ʍǝu Ɐ
of.message.java64Bit=§f˙ǝɔuɐɯɹoɟɹǝd ǝsɐǝɹɔuᴉ oʇ §eɐʌɐſ ʇᴉq-߈9§r ꞁꞁɐʇsuᴉ uɐɔ no⅄
of.message.openglError=§f(%2$s) %1$s :§eɹoɹɹƎ Ꞁ⅁uǝdO

of.message.shaders.loading=%s :sɹǝpɐɥs ᵷuᴉpɐoꞀ

of.message.other.reset=¿sǝnꞁɐʌ ʇꞁnɐɟǝp ɹᴉǝɥʇ oʇ sᵷuᴉʇʇǝs oǝpᴉʌ ꞁꞁɐ ʇǝsǝᴚ

of.message.loadingVisibleChunks=sʞunɥɔ ǝꞁqᴉsᴉʌ ᵷuᴉpɐoꞀ

# Skin customization

of.options.skinCustomisation.ofCape=˙˙˙ǝdɐƆ ǝuᴉℲᴉʇdO

of.options.capeOF.title=ǝdɐƆ ǝuᴉℲᴉʇdO
of.options.capeOF.openEditor=ɹoʇᴉpƎ ǝdɐƆ uǝdO
of.options.capeOF.reloadCape=ǝdɐƆ pɐoꞁǝᴚ
of.options.capeOF.copyEditorLink=pɹɐoqdᴉꞁƆ o⟘ ʞuᴉꞀ ʎdoƆ

of.message.capeOF.openEditor=˙ɹǝsʍoɹq qǝʍ ɐ uᴉ uǝdo pꞁnoɥs ɹoʇᴉpǝ ǝdɐɔ ǝuᴉℲᴉʇdO ǝɥ⟘
of.message.capeOF.openEditorError=˙ɹǝsʍoɹq qǝʍ ɐ uᴉ ʞuᴉꞁ ɹoʇᴉpǝ ǝɥʇ ᵷuᴉuǝdo ɹoɹɹƎ
of.message.capeOF.reloadCape=˙spuoɔǝs ϛ⥝ uᴉ pǝpɐoꞁǝɹ ǝq ꞁꞁᴉʍ ǝdɐɔ ǝɥ⟘

of.message.capeOF.error2=˙pǝꞁᴉɐɟ uoᴉʇɐɔᴉʇuǝɥʇnɐ ᵷuɐɾoW
of.message.capeOF.error1=%s :ɹoɹɹƎ

# Video settings

options.graphics.tooltip.7=ʎʇᴉꞁɐnb ꞁɐnsᴉΛ
options.graphics.tooltip.6=ɹǝʇsɐɟ 'ʎʇᴉꞁɐnb ɹǝʍoꞁ - ʇsɐℲ  
options.graphics.tooltip.5=ɹǝʍoꞁs 'ʎʇᴉꞁɐnb ɹǝɥᵷᴉɥ - ʎɔuɐℲ  
options.graphics.tooltip.4=ʇsǝʍoꞁs 'sʇɔǝɾqo ʇuǝɔnꞁsuɐɹʇ ɹǝʇʇǝq - snoꞁnqɐℲ  
options.graphics.tooltip.3='ɹǝʇɐʍ 'sǝʌɐǝꞁ 'spnoꞁɔ ɟo ǝɔuɐɹɐǝddɐ ǝɥʇ sǝᵷuɐɥƆ
options.graphics.tooltip.2=˙sǝpᴉs ssɐɹᵷ puɐ sʍopɐɥs
options.graphics.tooltip.1=˙sɹǝpɐɥs ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ ʇou sᴉ sɔᴉɥdɐɹᵷ snoꞁnqɐℲ

of.options.renderDistance.tiny=ʎuᴉ⟘
of.options.renderDistance.short=ʇɹoɥS
of.options.renderDistance.normal=ꞁɐɯɹoN
of.options.renderDistance.far=ɹɐℲ
of.options.renderDistance.extreme=ǝɯǝɹʇxƎ
of.options.renderDistance.insane=ǝuɐsuI
of.options.renderDistance.ludicrous=snoɹɔᴉpnꞀ

options.renderDistance.tooltip.8=ǝɔuɐʇsᴉp ǝꞁqᴉsᴉΛ
options.renderDistance.tooltip.7=(ʇsǝʇsɐɟ) ɯᘔƐ - ʎuᴉ⟘ ᘔ  
options.renderDistance.tooltip.6=(ꞁɐɯɹou) ɯ8ᘔ⥝ - ꞁɐɯɹoN 8  
options.renderDistance.tooltip.5=(ɹǝʍoꞁs) ɯ9ϛᘔ - ɹɐℲ 9⥝  
options.renderDistance.tooltip.4=ᵷuᴉpuɐɯǝp ǝɔɹnosǝɹ ʎɹǝʌ (¡ʇsǝʍoꞁs) ɯᘔ⥝ϛ - ǝɯǝɹʇxƎ ᘔƐ  
options.renderDistance.tooltip.3=pǝʇɐɔoꞁꞁɐ WⱯᴚ ᗺ⅁ᘔ spǝǝu 'ɯ89ㄥ - ǝuɐsuI 8߈  
options.renderDistance.tooltip.2=pǝʇɐɔoꞁꞁɐ WⱯᴚ ᗺ⅁Ɛ spǝǝu 'ɯ߈ᘔ0⥝ - snoɹɔᴉpnꞀ ߈9  
options.renderDistance.tooltip.1=˙spꞁɹoʍ ꞁɐɔoꞁ uᴉ ǝʌᴉʇɔǝɟɟǝ ʎꞁuo ǝɹɐ ɹɐℲ 9⥝ ɹǝʌo sǝnꞁɐΛ

options.entityDistanceScaling.tooltip.5=ǝɔuɐʇsᴉp ɹǝpuǝɹ ʎʇᴉʇuƎ
options.entityDistanceScaling.tooltip.4=ɹǝʇsɐɟ - %%0ϛ  
options.entityDistanceScaling.tooltip.3=ʇꞁnɐɟǝp - %%00⥝  
options.entityDistanceScaling.tooltip.2=ɹǝʍoꞁs - %%00ϛ  
options.entityDistanceScaling.tooltip.1=˙uʍoɥs ǝɹɐ sǝᴉʇᴉʇuǝ ɥɔᴉɥʍ ʇɐ ǝɔuɐʇsᴉp ɯnɯᴉxɐɯ ǝɥʇ sǝᴉɟᴉpoW

options.ao.tooltip.4=ᵷuᴉʇɥᵷᴉꞁ ɥʇooɯS
options.ao.tooltip.3=(ɹǝʇsɐɟ) ᵷuᴉʇɥᵷᴉꞁ ɥʇooɯs ou - ℲℲO  
options.ao.tooltip.2=(ɹǝʍoꞁs) ᵷuᴉʇɥᵷᴉꞁ ɥʇooɯs ǝꞁdɯᴉs - ɯnɯᴉuᴉW  
options.ao.tooltip.1=(ʇsǝʍoꞁs) ᵷuᴉʇɥᵷᴉꞁ ɥʇooɯs xǝꞁdɯoɔ - ɯnɯᴉxɐW  

options.framerateLimit.tooltip.6=ǝʇɐɹǝɯɐɹɟ xɐW
options.framerateLimit.tooltip.5=(0ᘔ '0Ɛ '09) ǝʇɐɹǝɯɐɹɟ ɹoʇᴉuoɯ oʇ ʇᴉɯᴉꞁ - ɔuʎSΛ  
options.framerateLimit.tooltip.4=ǝꞁqɐᴉɹɐʌ - ϛϛᘔ-ϛ  
options.framerateLimit.tooltip.3=(ʇsǝʇsɐɟ) ʇᴉɯᴉꞁ ou - pǝʇᴉɯᴉꞁu∩  
options.framerateLimit.tooltip.2=ɟᴉ uǝʌǝ SԀℲ ǝɥʇ sǝsɐǝɹɔǝp ʇᴉɯᴉꞁ ǝʇɐɹǝɯɐɹɟ ǝɥ⟘
options.framerateLimit.tooltip.1=˙pǝɥɔɐǝɹ ʇou sᴉ ǝnꞁɐʌ ʇᴉɯᴉꞁ ǝɥʇ
of.options.framerateLimit.vsync=ɔuʎSΛ

of.options.AO_LEVEL=ꞁǝʌǝꞀ ᵷuᴉʇɥᵷᴉꞀ ɥʇooɯS
of.options.AO_LEVEL.tooltip.4=ꞁǝʌǝꞁ ᵷuᴉʇɥᵷᴉꞁ ɥʇooɯS
of.options.AO_LEVEL.tooltip.3=sʍopɐɥs ou - ℲℲO  
of.options.AO_LEVEL.tooltip.2=sʍopɐɥs ʇɥᵷᴉꞁ - %%0ϛ  
of.options.AO_LEVEL.tooltip.1=sʍopɐɥs ʞɹɐp - %%00⥝  

options.viewBobbing.tooltip.2=˙ʇuǝɯǝʌoɯ ɔᴉʇsᴉꞁɐǝɹ ǝɹoW
options.viewBobbing.tooltip.1=˙sʇꞁnsǝɹ ʇsǝq ɹoɟ ℲℲO oʇ ʇᴉ ʇǝs sdɐɯdᴉɯ ᵷuᴉsn uǝɥM

options.guiScale.tooltip.6=ǝꞁɐɔS I∩⅁
options.guiScale.tooltip.5=ǝzᴉs ꞁɐɯᴉxɐɯ - oʇnⱯ  
options.guiScale.tooltip.4=xƐ oʇ x⥝ - ǝᵷɹɐꞀ 'ꞁɐɯɹoN 'ꞁꞁɐɯS  
options.guiScale.tooltip.3=sʎɐꞁdsᴉp Ʞ߈ uo ǝꞁqɐꞁᴉɐʌɐ - x0⥝ oʇ x߈  
options.guiScale.tooltip.2=˙ǝpoɔᴉu∩ ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ ʇou ǝɹɐ (˙˙˙ xϛ 'xƐ 'x⥝) sǝnꞁɐʌ ppO
options.guiScale.tooltip.1=˙ɹǝʇsɐɟ ǝq ʎɐɯ I∩⅁ ɹǝꞁꞁɐɯs Ɐ

options.vbo=sOᗺΛ ǝs∩
options.vbo.tooltip.3=sʇɔǝɾqO ɹǝɟɟnᗺ xǝʇɹǝΛ
options.vbo.tooltip.2=ʎꞁꞁɐnsn sᴉ ɥɔᴉɥʍ ꞁǝpoɯ ᵷuᴉɹǝpuǝɹ ǝʌᴉʇɐuɹǝʇꞁɐ uɐ sǝs∩
options.vbo.tooltip.1=˙ᵷuᴉɹǝpuǝɹ ʇꞁnɐɟǝp ǝɥʇ uɐɥʇ (%%0⥝-ϛ) ɹǝʇsɐɟ

options.gamma.tooltip.6=˙sʇɔǝɾqo ɹǝʞɹɐp ɟo ssǝuʇɥᵷᴉɹq ǝɥʇ sǝᵷuɐɥƆ
options.gamma.tooltip.5=ssǝuʇɥᵷᴉɹq pɹɐpuɐʇs - ʎpooW  
options.gamma.tooltip.4=ǝꞁqɐᴉɹɐʌ - %%66-⥝  
options.gamma.tooltip.3=sʇɔǝɾqo ɹǝʞɹɐp ɹoɟ ssǝuʇɥᵷᴉɹq ɯnɯᴉxɐɯ - ʇɥᵷᴉɹᗺ  
options.gamma.tooltip.2=ɟo ssǝuʇɥᵷᴉɹq ǝɥʇ ǝᵷuɐɥɔ ʇou sǝop uoᴉʇdo sᴉɥ⟘
options.gamma.tooltip.1=˙sʇɔǝɾqo ʞɔɐꞁq ʎꞁꞁnɟ

options.anaglyph.tooltip.4=ɥdʎꞁᵷɐuⱯ ᗡƐ
options.anaglyph.tooltip.3=sɹnoꞁoɔ ʇuǝɹǝɟɟᴉp ᵷuᴉsn ʇɔǝɟɟǝ ᗡƐ ɔᴉdoɔsoǝɹǝʇs ɐ sǝꞁqɐuƎ
options.anaglyph.tooltip.2=˙ǝʎǝ ɥɔɐǝ ɹoɟ
options.anaglyph.tooltip.1=˙ᵷuᴉʍǝᴉʌ ɹǝdoɹd ɹoɟ sǝssɐꞁᵷ uɐʎɔ-pǝɹ sǝɹᴉnbǝᴚ

options.attackIndicator.tooltip.6=ɹoʇɐɔᴉpuᴉ ʞɔɐʇʇɐ ǝɥʇ ɟo uoᴉʇᴉsod ǝɥʇ sǝɹnᵷᴉɟuoƆ
options.attackIndicator.tooltip.5=ɹᴉɐɥssoɹɔ ǝɥʇ ɹǝpun - ɹᴉɐɥssoɹƆ  
options.attackIndicator.tooltip.4=ɹɐqʇoɥ ǝɥʇ oʇ ʇxǝu - ɹɐqʇoH  
options.attackIndicator.tooltip.3=ɹoʇɐɔᴉpuᴉ ʞɔɐʇʇɐ ou - ℲℲO  
options.attackIndicator.tooltip.2=ǝɥʇ ɟo ɹǝʍod ʞɔɐʇʇɐ ǝɥʇ sʍoɥs ɹoʇɐɔᴉpuᴉ ʞɔɐʇʇɐ ǝɥ⟘
options.attackIndicator.tooltip.1=ɯǝʇᴉ pǝddᴉnbǝ ʎꞁʇuǝɹɹnɔ

of.options.ALTERNATE_BLOCKS=sʞɔoꞁᗺ ǝʇɐuɹǝʇꞁⱯ
of.options.ALTERNATE_BLOCKS.tooltip.3=sʞɔoꞁᗺ ǝʇɐuɹǝʇꞁⱯ
of.options.ALTERNATE_BLOCKS.tooltip.2=˙sʞɔoꞁq ǝɯos ɹoɟ sꞁǝpoɯ ʞɔoꞁq ǝʌᴉʇɐuɹǝʇꞁɐ sǝs∩
of.options.ALTERNATE_BLOCKS.tooltip.1=˙ʞɔɐd ǝɔɹnosǝɹ pǝʇɔǝꞁǝs ǝɥʇ uo spuǝdǝᗡ

of.options.FOG_FANCY=ᵷoℲ
of.options.FOG_FANCY.tooltip.6=ǝdʎʇ ᵷoℲ
of.options.FOG_FANCY.tooltip.5=ᵷoɟ ɹǝʇsɐɟ - ʇsɐℲ  
of.options.FOG_FANCY.tooltip.4=ɹǝʇʇǝq sʞooꞁ 'ᵷoɟ ɹǝʍoꞁs - ʎɔuɐℲ  
of.options.FOG_FANCY.tooltip.3=ʇsǝʇsɐɟ 'ᵷoɟ ou - ℲℲO  
of.options.FOG_FANCY.tooltip.2=ǝɥʇ ʎq pǝʇɹoddns sᴉ ʇᴉ ɟᴉ ʎꞁuo ǝꞁqɐꞁᴉɐʌɐ sᴉ ᵷoɟ ʎɔuɐɟ ǝɥ⟘
of.options.FOG_FANCY.tooltip.1=˙pɹɐɔ ɔᴉɥdɐɹᵷ

of.options.FOG_START=ʇɹɐʇS ᵷoℲ
of.options.FOG_START.tooltip.4=ʇɹɐʇs ᵷoℲ
of.options.FOG_START.tooltip.3=ɹǝʎɐꞁd ǝɥʇ ɹɐǝu sʇɹɐʇs ᵷoɟ ǝɥʇ - ᘔ˙0  
of.options.FOG_START.tooltip.2=ɹǝʎɐꞁd ǝɥʇ ɯoɹɟ ɹɐɟ sʇɹɐʇs ᵷoɟ ǝɥʇ - 8˙0  
of.options.FOG_START.tooltip.1=˙ǝɔuɐɯɹoɟɹǝd ǝɥʇ ʇɔǝɟɟɐ ʇou sǝop ʎꞁꞁɐnsn uoᴉʇdo sᴉɥ⟘

of.options.CHUNK_LOADING=ᵷuᴉpɐoꞀ ʞunɥƆ
of.options.CHUNK_LOADING.tooltip.8=ᵷuᴉpɐoꞀ ʞunɥƆ
of.options.CHUNK_LOADING.tooltip.7=sʞunɥɔ ᵷuᴉpɐoꞁ uǝɥʍ SԀℲ ǝꞁqɐʇsun - ʇꞁnɐɟǝᗡ  
of.options.CHUNK_LOADING.tooltip.6=SԀℲ ǝꞁqɐʇs - ɥʇooɯS  
of.options.CHUNK_LOADING.tooltip.5=ᵷuᴉpɐoꞁ pꞁɹoʍ ɹǝʇsɐɟ xƐ 'SԀℲ ǝꞁqɐʇs - ǝɹoƆ-ᴉʇꞁnW  
of.options.CHUNK_LOADING.tooltip.4=puɐ ᵷuᴉɹǝʇʇnʇs ǝɥʇ ǝʌoɯǝɹ ǝɹoƆ-ᴉʇꞁnW puɐ ɥʇooɯS
of.options.CHUNK_LOADING.tooltip.3=˙ᵷuᴉpɐoꞁ ʞunɥɔ ʎq pǝsnɐɔ sǝzǝǝɹɟ
of.options.CHUNK_LOADING.tooltip.2=puɐ ᵷuᴉpɐoꞁ pꞁɹoʍ ǝɥʇ xƐ dn pǝǝds uɐɔ ǝɹoƆ-ᴉʇꞁnW
of.options.CHUNK_LOADING.tooltip.1=˙ǝɹoɔ ∩ԀƆ puoɔǝs ɐ ᵷuᴉsn ʎq SԀℲ ǝsɐǝɹɔuᴉ
of.options.chunkLoading.smooth=ɥʇooɯS
of.options.chunkLoading.multiCore=ǝɹoƆ-ᴉʇꞁnW

of.options.shaders=˙˙˙sɹǝpɐɥS
of.options.shadersTitle=sɹǝpɐɥS

of.options.shaders.packNone=ℲℲO
of.options.shaders.packDefault=(ꞁɐuɹǝʇuᴉ)

of.options.shaders.ANTIALIASING=ᵷuᴉsɐᴉꞁɐᴉʇuⱯ
of.options.shaders.ANTIALIASING.tooltip.7=ᵷuᴉsɐᴉꞁɐᴉʇuⱯ
of.options.shaders.ANTIALIASING.tooltip.6=(ɹǝʇsɐɟ) ᵷuᴉsɐᴉꞁɐᴉʇuɐ ou (ʇꞁnɐɟǝp) - ℲℲO  
of.options.shaders.ANTIALIASING.tooltip.5=(ɹǝʍoꞁs) sǝᵷpǝ puɐ sǝuᴉꞁ pǝsɐᴉꞁɐᴉʇuɐ - x߈ 'xᘔ ⱯⱯXℲ  
of.options.shaders.ANTIALIASING.tooltip.4=sɥʇooɯs ɥɔᴉɥʍ ʇɔǝɟɟǝ ᵷuᴉssǝɔoɹd-ʇsod ɐ sᴉ ⱯⱯXℲ
of.options.shaders.ANTIALIASING.tooltip.3=˙suoᴉʇᴉsuɐɹʇ ɹoꞁoɔ dɹɐɥs puɐ sǝuᴉꞁ pǝᵷᵷɐɾ
of.options.shaders.ANTIALIASING.tooltip.2=ᵷuᴉsɐᴉꞁɐᴉʇuɐ ꞁɐuoᴉʇᴉpɐɹʇ uɐɥʇ ɹǝʇsɐɟ sᴉ ʇI
of.options.shaders.ANTIALIASING.tooltip.1=˙sɹǝpɐɥs ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ sᴉ puɐ

of.options.shaders.NORMAL_MAP=dɐW ꞁɐɯɹoN
of.options.shaders.NORMAL_MAP.tooltip.7=dɐW ꞁɐɯɹoN
of.options.shaders.NORMAL_MAP.tooltip.6=sdɐɯ ꞁɐɯɹou ǝꞁqɐuǝ (ʇꞁnɐɟǝp) - NO  
of.options.shaders.NORMAL_MAP.tooltip.5=sdɐɯ ꞁɐɯɹou ǝꞁqɐsᴉp - ℲℲO  
of.options.shaders.NORMAL_MAP.tooltip.4=ʞɔɐd ɹǝpɐɥs ǝɥʇ ʎq pǝsn ǝq uɐɔ sdɐɯ ꞁɐɯɹoN
of.options.shaders.NORMAL_MAP.tooltip.3=˙sǝɔɐɟɹns ꞁǝpoɯ ʇɐꞁɟ uo ʎɹʇǝɯoǝᵷ ᗡƐ ǝʇɐꞁnɯᴉs oʇ
of.options.shaders.NORMAL_MAP.tooltip.2=ǝɥʇ ʎq pǝᴉꞁddns ǝɹɐ sǝɹnʇxǝʇ dɐɯ ꞁɐɯɹou ǝɥ⟘
of.options.shaders.NORMAL_MAP.tooltip.1=˙ʞɔɐd ǝɔɹnosǝɹ ʇuǝɹɹnɔ

of.options.shaders.SPECULAR_MAP=dɐW ɹɐꞁnɔǝdS
of.options.shaders.SPECULAR_MAP.tooltip.7=dɐW ɹɐꞁnɔǝdS
of.options.shaders.SPECULAR_MAP.tooltip.6=sdɐɯ ɹɐꞁnɔǝds ǝꞁqɐuǝ (ʇꞁnɐɟǝp) - NO  
of.options.shaders.SPECULAR_MAP.tooltip.5=sdɐɯ ɹɐꞁnɔǝds ǝꞁqɐsᴉp - ℲℲO  
of.options.shaders.SPECULAR_MAP.tooltip.4=ʞɔɐd ɹǝpɐɥs ǝɥʇ ʎq pǝsn ǝq uɐɔ sdɐɯ ɹɐꞁnɔǝdS
of.options.shaders.SPECULAR_MAP.tooltip.3=˙sʇɔǝɟɟǝ uoᴉʇɔǝꞁɟǝɹ ꞁɐᴉɔǝds ǝʇɐꞁnɯᴉs oʇ
of.options.shaders.SPECULAR_MAP.tooltip.2=ǝɥʇ ʎq pǝᴉꞁddns ǝɹɐ sǝɹnʇxǝʇ dɐɯ ɹɐꞁnɔǝds ǝɥ⟘
of.options.shaders.SPECULAR_MAP.tooltip.1=˙ʞɔɐd ǝɔɹnosǝɹ ʇuǝɹɹnɔ

of.options.shaders.RENDER_RES_MUL=ʎʇᴉꞁɐnꝹ ɹǝpuǝᴚ
of.options.shaders.RENDER_RES_MUL.tooltip.8=ʎʇᴉꞁɐnꝹ ɹǝpuǝᴚ
of.options.shaders.RENDER_RES_MUL.tooltip.7=(ʇsǝʇsɐɟ) ʍoꞁ - xϛ˙0  
of.options.shaders.RENDER_RES_MUL.tooltip.6=(ʇꞁnɐɟǝp) pɹɐpuɐʇs - x⥝  
of.options.shaders.RENDER_RES_MUL.tooltip.5=(ʇsǝʍoꞁs) ɥᵷᴉɥ - xᘔ  
of.options.shaders.RENDER_RES_MUL.tooltip.4=ǝɹnʇxǝʇ ǝɥʇ ɟo ǝzᴉs ǝɥʇ sꞁoɹʇuoɔ ʎʇᴉꞁɐnb ɹǝpuǝᴚ
of.options.shaders.RENDER_RES_MUL.tooltip.3=˙oʇ ᵷuᴉɹǝpuǝɹ sᴉ ʞɔɐd ɹǝpɐɥs ǝɥʇ ʇɐɥʇ
of.options.shaders.RENDER_RES_MUL.tooltip.2=˙sʎɐꞁdsᴉp Ʞ߈ ɥʇᴉʍ ꞁnɟǝsn ǝq uɐɔ sǝnꞁɐʌ ɹǝʍoꞀ
of.options.shaders.RENDER_RES_MUL.tooltip.1=˙ɹǝʇꞁᴉɟ ᵷuᴉsɐᴉꞁɐᴉʇuɐ uɐ sɐ ʞɹoʍ sǝnꞁɐʌ ɹǝɥᵷᴉH

of.options.shaders.SHADOW_RES_MUL=ʎʇᴉꞁɐnꝹ ʍopɐɥS
of.options.shaders.SHADOW_RES_MUL.tooltip.8=ʎʇᴉꞁɐnꝹ ʍopɐɥS
of.options.shaders.SHADOW_RES_MUL.tooltip.7=(ʇsǝʇsɐɟ) ʍoꞁ - xϛ˙0  
of.options.shaders.SHADOW_RES_MUL.tooltip.6=(ʇꞁnɐɟǝp) pɹɐpuɐʇs - x⥝  
of.options.shaders.SHADOW_RES_MUL.tooltip.5=(ʇsǝʍoꞁs) ɥᵷᴉɥ - xᘔ  
of.options.shaders.SHADOW_RES_MUL.tooltip.4=dɐɯ ʍopɐɥs ǝɥʇ ɟo ǝzᴉs ǝɥʇ sꞁoɹʇuoɔ ʎʇᴉꞁɐnb ʍopɐɥS
of.options.shaders.SHADOW_RES_MUL.tooltip.3=˙ʞɔɐd ɹǝpɐɥs ǝɥʇ ʎq pǝsn ǝɹnʇxǝʇ
of.options.shaders.SHADOW_RES_MUL.tooltip.2=˙sʍopɐɥs ɹǝsɹɐoɔ 'ʇɔɐxǝuᴉ = sǝnꞁɐʌ ɹǝʍoꞀ
of.options.shaders.SHADOW_RES_MUL.tooltip.1=˙sʍopɐɥs ɹǝuᴉɟ 'pǝꞁᴉɐʇǝp = sǝnꞁɐʌ ɹǝɥᵷᴉH

of.options.shaders.HAND_DEPTH_MUL=ɥʇdǝᗡ puɐH
of.options.shaders.HAND_DEPTH_MUL.tooltip.8=ɥʇdǝᗡ puɐH
of.options.shaders.HAND_DEPTH_MUL.tooltip.7=ɐɹǝɯɐɔ ǝɥʇ oʇ ɹɐǝu puɐɥ - xϛ˙0  
of.options.shaders.HAND_DEPTH_MUL.tooltip.6=(ʇꞁnɐɟǝp) - x⥝  
of.options.shaders.HAND_DEPTH_MUL.tooltip.5=ɐɹǝɯɐɔ ǝɥʇ ɯoɹɟ ɹɐɟ puɐɥ - xᘔ  
of.options.shaders.HAND_DEPTH_MUL.tooltip.4=ǝɹɐ sʇɔǝɾqo pꞁǝɥpuɐɥ ǝɥʇ ɹɐɟ ʍoɥ sꞁoɹʇuoɔ ɥʇdǝp puɐH
of.options.shaders.HAND_DEPTH_MUL.tooltip.3=˙ɐɹǝɯɐɔ ǝɥʇ ɯoɹɟ
of.options.shaders.HAND_DEPTH_MUL.tooltip.2=ǝᵷuɐɥɔ pꞁnoɥs sᴉɥʇ ɹnꞁq ɥʇdǝp ᵷuᴉsn sʞɔɐd ɹǝpɐɥs ɹoℲ
of.options.shaders.HAND_DEPTH_MUL.tooltip.1=˙sʇɔǝɾqo pꞁǝɥpuɐɥ ɟo ᵷuᴉɹɹnꞁq ǝɥʇ

of.options.shaders.CLOUD_SHADOW=ʍopɐɥS pnoꞁƆ

of.options.shaders.OLD_HAND_LIGHT=ʇɥᵷᴉꞀ puɐH pꞁO
of.options.shaders.OLD_HAND_LIGHT.tooltip.7=ʇɥᵷᴉꞀ puɐH pꞁO
of.options.shaders.OLD_HAND_LIGHT.tooltip.6=ʞɔɐd ɹǝpɐɥs ǝɥʇ ʎq pǝꞁꞁoɹʇuoɔ - ʇꞁnɐɟǝᗡ  
of.options.shaders.OLD_HAND_LIGHT.tooltip.5=ʇɥᵷᴉꞁpuɐɥ pꞁo ǝsn - NO  
of.options.shaders.OLD_HAND_LIGHT.tooltip.4=ʇɥᵷᴉꞁpuɐɥ ʍǝu ǝsn - ℲℲO  
of.options.shaders.OLD_HAND_LIGHT.tooltip.3=ʎꞁuo ɥɔᴉɥʍ sʞɔɐd ɹǝpɐɥs sʍoꞁꞁɐ ʇɥᵷᴉꞁ puɐɥ pꞁO
of.options.shaders.OLD_HAND_LIGHT.tooltip.2=puɐɥ uᴉɐɯ ǝɥʇ uᴉ sɯǝʇᴉ ᵷuᴉʇʇᴉɯǝ ʇɥᵷᴉꞁ ǝsᴉuᵷoɔǝɹ
of.options.shaders.OLD_HAND_LIGHT.tooltip.1=˙puɐɥ-ɟɟo ǝɥʇ uᴉ sɯǝʇᴉ ɥʇᴉʍ ʞɹoʍ osꞁɐ oʇ

of.options.shaders.OLD_LIGHTING=ᵷuᴉʇɥᵷᴉꞀ pꞁO
of.options.shaders.OLD_LIGHTING.tooltip.8=ᵷuᴉʇɥᵷᴉꞀ pꞁO
of.options.shaders.OLD_LIGHTING.tooltip.7=ʞɔɐd ɹǝpɐɥs ǝɥʇ ʎq pǝꞁꞁoɹʇuoɔ - ʇꞁnɐɟǝᗡ  
of.options.shaders.OLD_LIGHTING.tooltip.6=ᵷuᴉʇɥᵷᴉꞁ pꞁo ǝsn - NO  
of.options.shaders.OLD_LIGHTING.tooltip.5=ᵷuᴉʇɥᵷᴉꞁ pꞁo ǝsn ʇou op - ℲℲO  
of.options.shaders.OLD_LIGHTING.tooltip.4=pǝᴉꞁddɐ ᵷuᴉʇɥᵷᴉꞁ pǝxᴉɟ ǝɥʇ sꞁoɹʇuoɔ ᵷuᴉʇɥᵷᴉꞁ pꞁO
of.options.shaders.OLD_LIGHTING.tooltip.3=˙sǝpᴉs ʞɔoꞁq ǝɥʇ oʇ ɐꞁꞁᴉuɐʌ ʎq
of.options.shaders.OLD_LIGHTING.tooltip.2=ǝpᴉʌoɹd ʎꞁꞁɐnsn sʍopɐɥs ǝsn ɥɔᴉɥʍ sʞɔɐd ɹǝpɐɥS
of.options.shaders.OLD_LIGHTING.tooltip.1=˙uoᴉʇᴉsod uns ǝɥʇ uo ᵷuᴉpuǝdǝp ᵷuᴉʇɥᵷᴉꞁ ɹǝʇʇǝq ɥɔnɯ

of.options.shaders.DOWNLOAD=sɹǝpɐɥS pɐoꞁuʍoᗡ
of.options.shaders.DOWNLOAD.tooltip.5=sɹǝpɐɥS pɐoꞁuʍoᗡ
of.options.shaders.DOWNLOAD.tooltip.4=
of.options.shaders.DOWNLOAD.tooltip.3=˙ɹǝsʍoɹq ɐ uᴉ ǝᵷɐd sʞɔɐd ɹǝpɐɥs ǝɥʇ suǝdO
of.options.shaders.DOWNLOAD.tooltip.2=,,ɹǝpꞁoℲ sɹǝpɐɥS,, ǝɥʇ uᴉ sʞɔɐd ɹǝpɐɥs pǝpɐoꞁuʍop ǝɥʇ ʇnԀ
of.options.shaders.DOWNLOAD.tooltip.1=˙sɹǝpɐɥs pǝꞁꞁɐʇsuᴉ ɟo ʇsᴉꞁ ǝɥʇ uᴉ ɹɐǝddɐ ꞁꞁᴉʍ ʎǝɥʇ puɐ

of.options.shaders.SHADER_PACK=ʞɔɐԀ ɹǝpɐɥS

of.options.shaders.shadersFolder=ɹǝpꞁoℲ sɹǝpɐɥS
of.options.shaders.shaderOptions=˙˙˙suoᴉʇdO ɹǝpɐɥS

of.options.shaderOptionsTitle=suoᴉʇdO ɹǝpɐɥS

of.options.quality=˙˙˙ʎʇᴉꞁɐnꝹ
of.options.qualityTitle=sᵷuᴉʇʇǝS ʎʇᴉꞁɐnꝹ

of.options.details=˙˙˙sꞁᴉɐʇǝᗡ
of.options.detailsTitle=sᵷuᴉʇʇǝS ꞁᴉɐʇǝᗡ

of.options.performance=˙˙˙ǝɔuɐɯɹoɟɹǝԀ
of.options.performanceTitle=sᵷuᴉʇʇǝS ǝɔuɐɯɹoɟɹǝԀ

of.options.animations=˙˙˙suoᴉʇɐɯᴉuⱯ
of.options.animationsTitle=sᵷuᴉʇʇǝS uoᴉʇɐɯᴉuⱯ

of.options.other=˙˙˙ɹǝɥʇO
of.options.otherTitle=sᵷuᴉʇʇǝS ɹǝɥʇO

of.options.other.reset=˙˙˙sᵷuᴉʇʇǝS oǝpᴉΛ ʇǝsǝᴚ

of.shaders.profile=ǝꞁᴉɟoɹԀ

# Quality

of.options.mipmap.bilinear=ɹɐǝuᴉꞁᴉᗺ
of.options.mipmap.linear=ɹɐǝuᴉꞀ
of.options.mipmap.nearest=ʇsǝɹɐǝN
of.options.mipmap.trilinear=ɹɐǝuᴉꞁᴉɹ⟘

options.mipmapLevels.tooltip.6=ɹǝʇʇǝq ʞooꞁ sʇɔǝɾqo ʇuɐʇsᴉp sǝʞɐɯ ɥɔᴉɥʍ ʇɔǝɟɟǝ ꞁɐnsᴉΛ
options.mipmapLevels.tooltip.5=sꞁᴉɐʇǝp ǝɹnʇxǝʇ ǝɥʇ ᵷuᴉɥʇooɯs ʎq
options.mipmapLevels.tooltip.4=ᵷuᴉɥʇooɯs ou - ℲℲO  
options.mipmapLevels.tooltip.3=ᵷuᴉɥʇooɯs ɯnɯᴉuᴉɯ - ⥝  
options.mipmapLevels.tooltip.2=ᵷuᴉɥʇooɯs ɯnɯᴉxɐɯ - ɯnɯᴉxɐW  
options.mipmapLevels.tooltip.1=˙ǝɔuɐɯɹoɟɹǝd ǝɥʇ ʇɔǝɟɟɐ ʇou sǝop ʎꞁꞁɐnsn uoᴉʇdo sᴉɥ⟘

of.options.MIPMAP_TYPE=ǝdʎ⟘ dɐɯdᴉW
of.options.MIPMAP_TYPE.tooltip.6=ɹǝʇʇǝq ʞooꞁ sʇɔǝɾqo ʇuɐʇsᴉp sǝʞɐɯ ɥɔᴉɥʍ ʇɔǝɟɟǝ ꞁɐnsᴉΛ
of.options.MIPMAP_TYPE.tooltip.5=sꞁᴉɐʇǝp ǝɹnʇxǝʇ ǝɥʇ ᵷuᴉɥʇooɯs ʎq
of.options.MIPMAP_TYPE.tooltip.4=(ʇsǝʇsɐɟ) ᵷuᴉɥʇooɯs ɥᵷnoɹ - ʇsǝɹɐǝN  
of.options.MIPMAP_TYPE.tooltip.3=ᵷuᴉɥʇooɯs ꞁɐɯɹou - ɹɐǝuᴉꞀ  
of.options.MIPMAP_TYPE.tooltip.2=ᵷuᴉɥʇooɯs ǝuᴉɟ - ɹɐǝuᴉꞁᴉᗺ  
of.options.MIPMAP_TYPE.tooltip.1=(ʇsǝʍoꞁs) ᵷuᴉɥʇooɯs ʇsǝuᴉɟ - ɹɐǝuᴉꞁᴉɹ⟘  


of.options.AA_LEVEL=ᵷuᴉsɐᴉꞁɐᴉʇuⱯ
of.options.AA_LEVEL.tooltip.8=ᵷuᴉsɐᴉꞁɐᴉʇuⱯ
of.options.AA_LEVEL.tooltip.7=(ɹǝʇsɐɟ) ᵷuᴉsɐᴉꞁɐᴉʇuɐ ou (ʇꞁnɐɟǝp) - ℲℲO 
of.options.AA_LEVEL.tooltip.6=(ɹǝʍoꞁs) sǝᵷpǝ puɐ sǝuᴉꞁ pǝsɐᴉꞁɐᴉʇuɐ - 9⥝-ᘔ 
of.options.AA_LEVEL.tooltip.5=puɐ sǝuᴉꞁ pǝᵷᵷɐɾ sɥʇooɯs ᵷuᴉsɐᴉꞁɐᴉʇuⱯ ǝɥ⟘
of.options.AA_LEVEL.tooltip.4=˙suoᴉʇᴉsuɐɹʇ ɹnoꞁoɔ dɹɐɥs
of.options.AA_LEVEL.tooltip.3=˙SԀℲ ǝɥʇ ǝsɐǝɹɔǝp ʎꞁꞁɐᴉʇuɐʇsqns ʎɐɯ ʇᴉ pǝꞁqɐuǝ uǝɥM
of.options.AA_LEVEL.tooltip.2=˙spɹɐɔ sɔᴉɥdɐɹᵷ ꞁꞁɐ ʎq pǝʇɹoddns ǝɹɐ sꞁǝʌǝꞁ ꞁꞁɐ ʇoN
of.options.AA_LEVEL.tooltip.1=¡⟘ᴚⱯ⟘SƎᴚ ɐ ɹǝʇɟɐ ǝʌᴉʇɔǝɟɟƎ

of.options.AF_LEVEL=ᵷuᴉɹǝʇꞁᴉℲ ɔᴉdoɹʇosᴉuⱯ
of.options.AF_LEVEL.tooltip.6=ᵷuᴉɹǝʇꞁᴉℲ ɔᴉdoɹʇosᴉuⱯ
of.options.AF_LEVEL.tooltip.5=(ɹǝʇsɐɟ) ꞁᴉɐʇǝp ǝɹnʇxǝʇ pɹɐpuɐʇs (ʇꞁnɐɟǝp) - ℲℲO 
of.options.AF_LEVEL.tooltip.4=(ɹǝʍoꞁs) sǝɹnʇxǝʇ pǝddɐɯdᴉɯ uᴉ sꞁᴉɐʇǝp ɹǝuᴉɟ - 9⥝-ᘔ 
of.options.AF_LEVEL.tooltip.3=uᴉ sꞁᴉɐʇǝp sǝɹoʇsǝɹ ᵷuᴉɹǝʇꞁᴉℲ ɔᴉdoɹʇosᴉuⱯ ǝɥ⟘
of.options.AF_LEVEL.tooltip.2=˙sǝɹnʇxǝʇ pǝddɐɯdᴉɯ
of.options.AF_LEVEL.tooltip.1=˙SԀℲ ǝɥʇ ǝsɐǝɹɔǝp ʎꞁꞁɐᴉʇuɐʇsqns ʎɐɯ ʇᴉ pǝꞁqɐuǝ uǝɥM

of.options.CLEAR_WATER=ɹǝʇɐM ɹɐǝꞁƆ
of.options.CLEAR_WATER.tooltip.3=ɹǝʇɐM ɹɐǝꞁƆ
of.options.CLEAR_WATER.tooltip.2=ɹǝʇɐʍ ʇuǝɹɐdsuɐɹʇ 'ɹɐǝꞁɔ - NO  
of.options.CLEAR_WATER.tooltip.1=ɹǝʇɐʍ ʇꞁnɐɟǝp - ℲℲO  

of.options.RANDOM_ENTITIES=sǝᴉʇᴉʇuƎ ɯopuɐᴚ
of.options.RANDOM_ENTITIES.tooltip.5=sǝᴉʇᴉʇuƎ ɯopuɐᴚ
of.options.RANDOM_ENTITIES.tooltip.4=ɹǝʇsɐɟ 'sǝᴉʇᴉʇuǝ ɯopuɐɹ ou - ℲℲO  
of.options.RANDOM_ENTITIES.tooltip.3=ɹǝʍoꞁs 'sǝᴉʇᴉʇuǝ ɯopuɐɹ - NO  
of.options.RANDOM_ENTITIES.tooltip.2=˙sǝᴉʇᴉʇuǝ ǝɯɐᵷ ǝɥʇ ɹoɟ sǝɹnʇxǝʇ ɯopuɐɹ sǝsn sǝᴉʇᴉʇuǝ ɯopuɐᴚ
of.options.RANDOM_ENTITIES.tooltip.1=˙sǝɹnʇxǝʇ ʎʇᴉʇuǝ ǝꞁdᴉʇꞁnɯ sɐɥ ɥɔᴉɥʍ ʞɔɐd ǝɔɹnosǝɹ ɐ spǝǝu ʇI

of.options.BETTER_GRASS=ssɐɹ⅁ ɹǝʇʇǝᗺ
of.options.BETTER_GRASS.tooltip.4=ssɐɹ⅁ ɹǝʇʇǝᗺ
of.options.BETTER_GRASS.tooltip.3=ʇsǝʇsɐɟ 'ǝɹnʇxǝʇ ssɐɹᵷ ǝpᴉs ʇꞁnɐɟǝp - ℲℲO  
of.options.BETTER_GRASS.tooltip.2=ɹǝʍoꞁs 'ǝɹnʇxǝʇ ssɐɹᵷ ǝpᴉs ꞁꞁnɟ - ʇsɐℲ  
of.options.BETTER_GRASS.tooltip.1=ʇsǝʍoꞁs 'ǝɹnʇxǝʇ ssɐɹᵷ ǝpᴉs ɔᴉɯɐuʎp - ʎɔuɐℲ  

of.options.BETTER_SNOW=ʍouS ɹǝʇʇǝᗺ
of.options.BETTER_SNOW.tooltip.5=ʍouS ɹǝʇʇǝᗺ
of.options.BETTER_SNOW.tooltip.4=ɹǝʇsɐɟ 'ʍous ʇꞁnɐɟǝp - ℲℲO  
of.options.BETTER_SNOW.tooltip.3=ɹǝʍoꞁs 'ʍous ɹǝʇʇǝq - NO  
of.options.BETTER_SNOW.tooltip.2=(ssɐɹᵷ ꞁꞁɐʇ 'ǝɔuǝɟ) sʞɔoꞁq ʇuǝɹɐdsuɐɹʇ ɹǝpun ʍous sʍoɥS
of.options.BETTER_SNOW.tooltip.1=˙sʞɔoꞁq ʍous ɥʇᴉʍ ᵷuᴉɹǝpɹoq uǝɥʍ

of.options.CUSTOM_FONTS=sʇuoℲ ɯoʇsnƆ
of.options.CUSTOM_FONTS.tooltip.5=sʇuoℲ ɯoʇsnƆ
of.options.CUSTOM_FONTS.tooltip.4=ɹǝʍoꞁs '(ʇꞁnɐɟǝp) sʇuoɟ ɯoʇsnɔ sǝsn - NO  
of.options.CUSTOM_FONTS.tooltip.3=ɹǝʇsɐɟ 'ʇuoɟ ʇꞁnɐɟǝp sǝsn - ℲℲO  
of.options.CUSTOM_FONTS.tooltip.2=ʇuǝɹɹnɔ ǝɥʇ ʎq pǝᴉꞁddns ǝɹɐ sʇuoɟ ɯoʇsnɔ ǝɥ⟘
of.options.CUSTOM_FONTS.tooltip.1=˙ʞɔɐd ǝɔɹnosǝɹ

of.options.CUSTOM_COLORS=sɹnoꞁoƆ ɯoʇsnƆ
of.options.CUSTOM_COLORS.tooltip.5=sɹnoꞁoƆ ɯoʇsnƆ
of.options.CUSTOM_COLORS.tooltip.4=ɹǝʍoꞁs '(ʇꞁnɐɟǝp) sɹnoꞁoɔ ɯoʇsnɔ sǝsn - NO  
of.options.CUSTOM_COLORS.tooltip.3=ɹǝʇsɐɟ 'sɹnoꞁoɔ ʇꞁnɐɟǝp sǝsn - ℲℲO  
of.options.CUSTOM_COLORS.tooltip.2=ʇuǝɹɹnɔ ǝɥʇ ʎq pǝᴉꞁddns ǝɹɐ sɹnoꞁoɔ ɯoʇsnɔ ǝɥ⟘
of.options.CUSTOM_COLORS.tooltip.1=˙ʞɔɐd ǝɔɹnosǝɹ

of.options.SWAMP_COLORS=sɹnoꞁoƆ dɯɐʍS
of.options.SWAMP_COLORS.tooltip.4=sɹnoꞁoƆ dɯɐʍS
of.options.SWAMP_COLORS.tooltip.3=ɹǝʍoꞁs '(ʇꞁnɐɟǝp) sɹnoꞁoɔ dɯɐʍs ǝsn - NO  
of.options.SWAMP_COLORS.tooltip.2=ɹǝʇsɐɟ 'sɹnoꞁoɔ dɯɐʍs ǝsn ʇou op - ℲℲO  
of.options.SWAMP_COLORS.tooltip.1=˙ɹǝʇɐʍ puɐ sǝuᴉʌ 'sǝʌɐǝꞁ 'ssɐɹᵷ ʇɔǝɟɟɐ sɹnoꞁoɔ dɯɐʍs ǝɥ⟘

of.options.SMOOTH_BIOMES=sǝɯoᴉᗺ ɥʇooɯS
of.options.SMOOTH_BIOMES.tooltip.6=sǝɯoᴉᗺ ɥʇooɯS
of.options.SMOOTH_BIOMES.tooltip.5=ɹǝʍoꞁs '(ʇꞁnɐɟǝp) sɹǝpɹoq ǝɯoᴉq ɟo ᵷuᴉɥʇooɯs - NO  
of.options.SMOOTH_BIOMES.tooltip.4=ɹǝʇsɐɟ 'sɹǝpɹoq ǝɯoᴉq ɟo ᵷuᴉɥʇooɯs ou - ℲℲO  
of.options.SMOOTH_BIOMES.tooltip.3=puɐ ᵷuᴉꞁdɯɐs ʎq ǝuop sᴉ sɹǝpɹoq ǝɯoᴉq ɟo ᵷuᴉɥʇooɯs ǝɥ⟘
of.options.SMOOTH_BIOMES.tooltip.2=˙sʞɔoꞁq ᵷuᴉpunoɹɹns ꞁꞁɐ ɟo ɹnoꞁoɔ ǝɥʇ ᵷuᴉᵷɐɹǝʌɐ
of.options.SMOOTH_BIOMES.tooltip.1=˙ɹǝʇɐʍ puɐ sǝuᴉʌ 'sǝʌɐǝꞁ 'ssɐɹᵷ ǝɹɐ pǝʇɔǝɟɟⱯ

of.options.CONNECTED_TEXTURES=sǝɹnʇxǝ⟘ pǝʇɔǝuuoƆ
of.options.CONNECTED_TEXTURES.tooltip.8=sǝɹnʇxǝ⟘ pǝʇɔǝuuoƆ
of.options.CONNECTED_TEXTURES.tooltip.7=(ʇꞁnɐɟǝp) sǝɹnʇxǝʇ pǝʇɔǝuuoɔ ou - ℲℲO  
of.options.CONNECTED_TEXTURES.tooltip.6=sǝɹnʇxǝʇ pǝʇɔǝuuoɔ ʇsɐɟ - ʇsɐℲ  
of.options.CONNECTED_TEXTURES.tooltip.5=sǝɹnʇxǝʇ pǝʇɔǝuuoɔ ʎɔuɐɟ - ʎɔuɐℲ  
of.options.CONNECTED_TEXTURES.tooltip.4='ssɐꞁᵷ ɟo sǝɹnʇxǝʇ ǝɥʇ suᴉoɾ sǝɹnʇxǝʇ pǝʇɔǝuuoƆ
of.options.CONNECTED_TEXTURES.tooltip.3=oʇ ʇxǝu pǝɔɐꞁd uǝɥʍ sǝʌꞁǝɥsʞooq puɐ ǝuoʇspuɐs
of.options.CONNECTED_TEXTURES.tooltip.2=pǝᴉꞁddns ǝɹɐ sǝɹnʇxǝʇ pǝʇɔǝuuoɔ ǝɥ⟘ ˙ɹǝɥʇo ɥɔɐǝ
of.options.CONNECTED_TEXTURES.tooltip.1=˙ʞɔɐd ǝɔɹnosǝɹ ʇuǝɹɹnɔ ǝɥʇ ʎq

of.options.NATURAL_TEXTURES=sǝɹnʇxǝ⟘ ꞁɐɹnʇɐN
of.options.NATURAL_TEXTURES.tooltip.8=sǝɹnʇxǝ⟘ ꞁɐɹnʇɐN
of.options.NATURAL_TEXTURES.tooltip.7=(ʇꞁnɐɟǝp) sǝɹnʇxǝʇ ꞁɐɹnʇɐu ou - ℲℲO  
of.options.NATURAL_TEXTURES.tooltip.6=sǝɹnʇxǝʇ ꞁɐɹnʇɐu ǝsn - NO  
of.options.NATURAL_TEXTURES.tooltip.5=uɹǝʇʇɐd ǝʞᴉꞁpᴉɹᵷ ǝɥʇ ǝʌoɯǝɹ sǝɹnʇxǝʇ ꞁɐɹnʇɐN
of.options.NATURAL_TEXTURES.tooltip.4=˙ǝdʎʇ ǝɯɐs ǝɥʇ ɟo sʞɔoꞁq ᵷuᴉʇɐǝdǝɹ ʎq pǝʇɐǝɹɔ
of.options.NATURAL_TEXTURES.tooltip.3=ǝsɐq ǝɥʇ ɟo sʇuɐᴉɹɐʌ pǝddᴉꞁɟ puɐ pǝʇɐʇoɹ sǝsn ʇI
of.options.NATURAL_TEXTURES.tooltip.2=ꞁɐɹnʇɐu ǝɥʇ ɹoɟ uoᴉʇɐɹnᵷᴉɟuoɔ ǝɥ⟘ ˙ǝɹnʇxǝʇ ʞɔoꞁq
of.options.NATURAL_TEXTURES.tooltip.1=˙ʞɔɐd ǝɔɹnosǝɹ ʇuǝɹɹnɔ ǝɥʇ ʎq pǝᴉꞁddns sᴉ sǝɹnʇxǝʇ

of.options.EMISSIVE_TEXTURES=sǝɹnʇxǝ⟘ ǝʌᴉssᴉɯƎ
of.options.EMISSIVE_TEXTURES.tooltip.8=sǝɹnʇxǝ⟘ ǝʌᴉssᴉɯƎ
of.options.EMISSIVE_TEXTURES.tooltip.7=(ʇꞁnɐɟǝp) sǝɹnʇxǝʇ ǝʌᴉssᴉɯǝ ou - ℲℲO  
of.options.EMISSIVE_TEXTURES.tooltip.6=sǝɹnʇxǝʇ ǝʌᴉssᴉɯǝ ǝsn - NO  
of.options.EMISSIVE_TEXTURES.tooltip.5=sʎɐꞁɹǝʌo sɐ pǝɹǝpuǝɹ ǝɹɐ sǝɹnʇxǝʇ ǝʌᴉssᴉɯǝ ǝɥ⟘
of.options.EMISSIVE_TEXTURES.tooltip.4=ǝʇɐꞁnɯᴉs oʇ pǝsn ǝq uɐɔ ʎǝɥ⟘ ˙ssǝuʇɥᵷᴉɹq ꞁꞁnɟ ɥʇᴉʍ
of.options.EMISSIVE_TEXTURES.tooltip.3=˙ǝɹnʇxǝʇ ǝsɐq ǝɥʇ ɟo sʇɹɐd ᵷuᴉʇʇᴉɯǝ ʇɥᵷᴉꞁ
of.options.EMISSIVE_TEXTURES.tooltip.2=ʇuǝɹɹnɔ ǝɥʇ ʎq pǝᴉꞁddns ǝɹɐ sǝɹnʇxǝʇ ǝʌᴉssᴉɯǝ ǝɥ⟘
of.options.EMISSIVE_TEXTURES.tooltip.1=˙ʞɔɐd ǝɔɹnosǝɹ

of.options.CUSTOM_SKY=ʎʞS ɯoʇsnƆ
of.options.CUSTOM_SKY.tooltip.5=ʎʞS ɯoʇsnƆ
of.options.CUSTOM_SKY.tooltip.4=ʍoꞁs '(ʇꞁnɐɟǝp) sǝɹnʇxǝʇ ʎʞs ɯoʇsnɔ - NO  
of.options.CUSTOM_SKY.tooltip.3=ɹǝʇsɐɟ 'ʎʞs ʇꞁnɐɟǝp - ℲℲO  
of.options.CUSTOM_SKY.tooltip.2=ʇuǝɹɹnɔ ǝɥʇ ʎq pǝᴉꞁddns ǝɹɐ sǝɹnʇxǝʇ ʎʞs ɯoʇsnɔ ǝɥ⟘
of.options.CUSTOM_SKY.tooltip.1=˙ʞɔɐd ǝɔɹnosǝɹ

of.options.CUSTOM_ITEMS=sɯǝʇI ɯoʇsnƆ
of.options.CUSTOM_ITEMS.tooltip.5=sɯǝʇI ɯoʇsnƆ
of.options.CUSTOM_ITEMS.tooltip.4=ʍoꞁs '(ʇꞁnɐɟǝp) sǝɹnʇxǝʇ ɯǝʇᴉ ɯoʇsnɔ - NO  
of.options.CUSTOM_ITEMS.tooltip.3=ɹǝʇsɐɟ 'sǝɹnʇxǝʇ ɯǝʇᴉ ʇꞁnɐɟǝp - ℲℲO  
of.options.CUSTOM_ITEMS.tooltip.2=ʇuǝɹɹnɔ ǝɥʇ ʎq pǝᴉꞁddns ǝɹɐ sǝɹnʇxǝʇ ɯǝʇᴉ ɯoʇsnɔ ǝɥ⟘
of.options.CUSTOM_ITEMS.tooltip.1=˙ʞɔɐd ǝɔɹnosǝɹ

of.options.CUSTOM_ENTITY_MODELS=sꞁǝpoW ʎʇᴉʇuƎ ɯoʇsnƆ
of.options.CUSTOM_ENTITY_MODELS.tooltip.5=sꞁǝpoW ʎʇᴉʇuƎ ɯoʇsnƆ
of.options.CUSTOM_ENTITY_MODELS.tooltip.4=ʍoꞁs '(ʇꞁnɐɟǝp) sꞁǝpoɯ ʎʇᴉʇuǝ ɯoʇsnɔ - NO  
of.options.CUSTOM_ENTITY_MODELS.tooltip.3=ɹǝʇsɐɟ 'sꞁǝpoɯ ʎʇᴉʇuǝ ʇꞁnɐɟǝp - ℲℲO  
of.options.CUSTOM_ENTITY_MODELS.tooltip.2=ʇuǝɹɹnɔ ǝɥʇ ʎq pǝᴉꞁddns ǝɹɐ sꞁǝpoɯ ʎʇᴉʇuǝ ɯoʇsnɔ ǝɥ⟘
of.options.CUSTOM_ENTITY_MODELS.tooltip.1=˙ʞɔɐd ǝɔɹnosǝɹ

of.options.CUSTOM_GUIS=sI∩⅁ ɯoʇsnƆ
of.options.CUSTOM_GUIS.tooltip.4=sI∩⅁ ɯoʇsnƆ
of.options.CUSTOM_GUIS.tooltip.3=ɹǝʍoꞁs '(ʇꞁnɐɟǝp) sI∩⅁ ɯoʇsnɔ - NO  
of.options.CUSTOM_GUIS.tooltip.2=ɹǝʇsɐɟ 'sI∩⅁ ʇꞁnɐɟǝp - ℲℲO  
of.options.CUSTOM_GUIS.tooltip.1=˙ʞɔɐd ǝɔɹnosǝɹ ʇuǝɹɹnɔ ǝɥʇ ʎq pǝᴉꞁddns ǝɹɐ sI∩⅁ ɯoʇsnɔ ǝɥ⟘

# Details

of.options.CLOUDS=spnoꞁƆ
of.options.CLOUDS.tooltip.7=spnoꞁƆ
of.options.CLOUDS.tooltip.6=sɔᴉɥdɐɹ⅁ ᵷuᴉʇʇǝs ʎq ʇǝs sɐ - ʇꞁnɐɟǝᗡ  
of.options.CLOUDS.tooltip.5=ɹǝʇsɐɟ 'ʎʇᴉꞁɐnb ɹǝʍoꞁ - ʇsɐℲ  
of.options.CLOUDS.tooltip.4=ɹǝʍoꞁs 'ʎʇᴉꞁɐnb ɹǝɥᵷᴉɥ - ʎɔuɐℲ  
of.options.CLOUDS.tooltip.3=ʇsǝʇsɐɟ 'spnoꞁɔ ou - ℲℲO  
of.options.CLOUDS.tooltip.2=˙ᗡᘔ pǝɹǝpuǝɹ ǝɹɐ spnoꞁɔ ʇsɐℲ
of.options.CLOUDS.tooltip.1=˙ᗡƐ pǝɹǝpuǝɹ ǝɹɐ spnoꞁɔ ʎɔuɐℲ

of.options.CLOUD_HEIGHT=ʇɥᵷᴉǝH pnoꞁƆ
of.options.CLOUD_HEIGHT.tooltip.3=ʇɥᵷᴉǝH pnoꞁƆ
of.options.CLOUD_HEIGHT.tooltip.2=ʇɥᵷᴉǝɥ ʇꞁnɐɟǝp - ℲℲO  
of.options.CLOUD_HEIGHT.tooltip.1=ʇᴉɯᴉꞁ ʇɥᵷᴉǝɥ pꞁɹoʍ ǝʌoqɐ - %%00⥝  

of.options.TREES=sǝǝɹ⟘
of.options.TREES.tooltip.7=sǝǝɹ⟘
of.options.TREES.tooltip.6=sɔᴉɥdɐɹ⅁ ᵷuᴉʇʇǝs ʎq ʇǝs sɐ - ʇꞁnɐɟǝᗡ  
of.options.TREES.tooltip.5=ɹǝʇsɐɟ 'ʎʇᴉꞁɐnb ɹǝʍoꞁ - ʇsɐℲ  
of.options.TREES.tooltip.4=ʇsɐɟ 'ʎʇᴉꞁɐnb ɹǝɥᵷᴉɥ - ʇɹɐɯS  
of.options.TREES.tooltip.3=ɹǝʍoꞁs 'ʎʇᴉꞁɐnb ʇsǝɥᵷᴉɥ - ʎɔuɐℲ  
of.options.TREES.tooltip.2=˙sǝʌɐǝꞁ ǝnbɐdo ǝʌɐɥ sǝǝɹʇ ʇsɐℲ
of.options.TREES.tooltip.1=˙sǝʌɐǝꞁ ʇuǝɹɐdsuɐɹʇ ǝʌɐɥ sǝǝɹʇ ʇɹɐɯs puɐ ʎɔuɐℲ

of.options.RAIN=ʍouS ⅋ uᴉɐᴚ
of.options.RAIN.tooltip.7=ʍouS ⅋ uᴉɐᴚ
of.options.RAIN.tooltip.6=sɔᴉɥdɐɹ⅁ ᵷuᴉʇʇǝs ʎq ʇǝs sɐ - ʇꞁnɐɟǝᗡ  
of.options.RAIN.tooltip.5=ɹǝʇsɐɟ 'ʍous/uᴉɐɹ ʇɥᵷᴉꞁ -  ʇsɐℲ  
of.options.RAIN.tooltip.4=ɹǝʍoꞁs 'ʍous/uᴉɐɹ ʎʌɐǝɥ - ʎɔuɐℲ  
of.options.RAIN.tooltip.3=ʇsǝʇsɐɟ 'ʍous/uᴉɐɹ ou - ℲℲO  
of.options.RAIN.tooltip.2=spunos uᴉɐɹ puɐ sǝɥsɐꞁds ǝɥʇ ℲℲO sᴉ uᴉɐɹ uǝɥM
of.options.RAIN.tooltip.1=˙ǝʌᴉʇɔɐ ꞁꞁᴉʇs ǝɹɐ

of.options.SKY=ʎʞS
of.options.SKY.tooltip.4=ʎʞS
of.options.SKY.tooltip.3=ɹǝʍoꞁs 'ǝꞁqᴉsᴉʌ sᴉ ʎʞs - NO  
of.options.SKY.tooltip.2=ɹǝʇsɐɟ 'ǝꞁqᴉsᴉʌ ʇou sᴉ ʎʞs -  ℲℲO  
of.options.SKY.tooltip.1=˙ǝꞁqᴉsᴉʌ ꞁꞁᴉʇs ǝɹɐ uns puɐ uooɯ ǝɥʇ ℲℲO sᴉ ʎʞs uǝɥM

of.options.STARS=sɹɐʇS
of.options.STARS.tooltip.3=sɹɐʇS
of.options.STARS.tooltip.2=ɹǝʍoꞁs 'ǝꞁqᴉsᴉʌ ǝɹɐ sɹɐʇs - NO  
of.options.STARS.tooltip.1=ɹǝʇsɐɟ 'ǝꞁqᴉsᴉʌ ʇou ǝɹɐ sɹɐʇs -  ℲℲO  

of.options.SUN_MOON=uooW ⅋ unS
of.options.SUN_MOON.tooltip.3=uooW ⅋ unS
of.options.SUN_MOON.tooltip.2=(ʇꞁnɐɟǝp) ǝꞁqᴉsᴉʌ ǝɹɐ uooɯ puɐ uns - NO  
of.options.SUN_MOON.tooltip.1=(ɹǝʇsɐɟ) ǝꞁqᴉsᴉʌ ʇou ǝɹɐ uooɯ puɐ uns -  ℲℲO  

of.options.SHOW_CAPES=sǝdɐƆ ʍoɥS
of.options.SHOW_CAPES.tooltip.3=sǝdɐƆ ʍoɥS
of.options.SHOW_CAPES.tooltip.2=(ʇꞁnɐɟǝp) sǝdɐɔ ɹǝʎɐꞁd ʍoɥs - NO  
of.options.SHOW_CAPES.tooltip.1=sǝdɐɔ ɹǝʎɐꞁd ʍoɥs ʇou op - ℲℲO  

of.options.TRANSLUCENT_BLOCKS=sʞɔoꞁᗺ ʇuǝɔnꞁsuɐɹ⟘
of.options.TRANSLUCENT_BLOCKS.tooltip.7=sʞɔoꞁᗺ ʇuǝɔnꞁsuɐɹ⟘
of.options.TRANSLUCENT_BLOCKS.tooltip.6=sɔᴉɥdɐɹ⅁ ᵷuᴉʇʇǝs ʎq ʇǝs sɐ - ʇꞁnɐɟǝᗡ  
of.options.TRANSLUCENT_BLOCKS.tooltip.5=(ʇꞁnɐɟǝp) ᵷuᴉpuǝꞁq ɹnoꞁoɔ ʇɔǝɹɹoɔ - ʎɔuɐℲ  
of.options.TRANSLUCENT_BLOCKS.tooltip.4=(ɹǝʇsɐɟ) ᵷuᴉpuǝꞁq ɹnoꞁoɔ ʇsɐɟ - ʇsɐℲ  
of.options.TRANSLUCENT_BLOCKS.tooltip.3=sʞɔoꞁq ʇuǝɔnꞁsuɐɹʇ ɟo ᵷuᴉpuǝꞁq ɹnoꞁoɔ ǝɥʇ sꞁoɹʇuoƆ
of.options.TRANSLUCENT_BLOCKS.tooltip.2=(ǝɔᴉ 'ɹǝʇɐʍ 'ssɐꞁᵷ pǝuᴉɐʇs) ɹnoꞁoɔ ʇuǝɹǝɟɟᴉp ɥʇᴉʍ
of.options.TRANSLUCENT_BLOCKS.tooltip.1=˙ɯǝɥʇ uǝǝʍʇǝq ɹᴉɐ ɥʇᴉʍ ɹǝɥʇo ɥɔɐǝ puᴉɥǝq pǝɔɐꞁd uǝɥʍ

of.options.HELD_ITEM_TOOLTIPS=sdᴉʇꞁoo⟘ ɯǝʇI pꞁǝH
of.options.HELD_ITEM_TOOLTIPS.tooltip.3=sdᴉʇꞁooʇ ɯǝʇᴉ pꞁǝH
of.options.HELD_ITEM_TOOLTIPS.tooltip.2=(ʇꞁnɐɟǝp) sɯǝʇᴉ pꞁǝɥ ɹoɟ sdᴉʇꞁooʇ ʍoɥs - NO  
of.options.HELD_ITEM_TOOLTIPS.tooltip.1=sɯǝʇᴉ pꞁǝɥ ɹoɟ sdᴉʇꞁooʇ ʍoɥs ʇou op - ℲℲO  

of.options.ADVANCED_TOOLTIPS=sdᴉʇꞁoo⟘ pǝɔuɐʌpⱯ
of.options.ADVANCED_TOOLTIPS.tooltip.6=sdᴉʇꞁooʇ pǝɔuɐʌpⱯ
of.options.ADVANCED_TOOLTIPS.tooltip.5=sdᴉʇꞁooʇ pǝɔuɐʌpɐ ʍoɥs - NO  
of.options.ADVANCED_TOOLTIPS.tooltip.4=(ʇꞁnɐɟǝp) sdᴉʇꞁooʇ pǝɔuɐʌpɐ ʍoɥs ʇou op - ℲℲO  
of.options.ADVANCED_TOOLTIPS.tooltip.3=ɹoɟ uoᴉʇɐɯɹoɟuᴉ pǝpuǝʇxǝ ʍoɥs sdᴉʇꞁooʇ pǝɔuɐʌpⱯ
of.options.ADVANCED_TOOLTIPS.tooltip.2=suoᴉʇdo ɹǝpɐɥs ɹoɟ puɐ (ʎʇᴉꞁᴉqɐɹnp 'pᴉ) sɯǝʇᴉ
of.options.ADVANCED_TOOLTIPS.tooltip.1=˙(ǝnꞁɐʌ ʇꞁnɐɟǝp 'ǝɔɹnos 'pᴉ)

of.options.DROPPED_ITEMS=sɯǝʇI pǝddoɹᗡ
of.options.DROPPED_ITEMS.tooltip.4=sɯǝʇI pǝddoɹᗡ
of.options.DROPPED_ITEMS.tooltip.3=sɔᴉɥdɐɹ⅁ ᵷuᴉʇʇǝs ʎq ʇǝs sɐ - ʇꞁnɐɟǝᗡ  
of.options.DROPPED_ITEMS.tooltip.2=(ɹǝʇsɐɟ) sɯǝʇᴉ pǝddoɹp ᗡᘔ - ʇsɐℲ  
of.options.DROPPED_ITEMS.tooltip.1=(ɹǝʍoꞁs) sɯǝʇᴉ pǝddoɹp ᗡƐ - ʎɔuɐℲ  

options.entityShadows.tooltip.3=sʍopɐɥS ʎʇᴉʇuƎ
options.entityShadows.tooltip.2=sʍopɐɥs ʎʇᴉʇuǝ ʍoɥs - NO  
options.entityShadows.tooltip.1=sʍopɐɥs ʎʇᴉʇuǝ ʍoɥs ʇou op - ℲℲO  

of.options.VIGNETTE=ǝʇʇǝuᵷᴉΛ
of.options.VIGNETTE.tooltip.8=sɹǝuɹoɔ uǝǝɹɔs ǝɥʇ suǝʞɹɐp ʎꞁʇɥᵷᴉꞁs ɥɔᴉɥʍ ʇɔǝɟɟǝ ꞁɐnsᴉΛ
of.options.VIGNETTE.tooltip.7=(ʇꞁnɐɟǝp) sɔᴉɥdɐɹ⅁ ᵷuᴉʇʇǝs ǝɥʇ ʎq ʇǝs sɐ - ʇꞁnɐɟǝᗡ  
of.options.VIGNETTE.tooltip.6=(ɹǝʇsɐɟ) pǝꞁqɐsᴉp ǝʇʇǝuᵷᴉʌ - ʇsɐℲ  
of.options.VIGNETTE.tooltip.5=(ɹǝʍoꞁs) pǝꞁqɐuǝ ǝʇʇǝuᵷᴉʌ - ʎɔuɐℲ  
of.options.VIGNETTE.tooltip.4='SԀℲ ǝɥʇ uo ʇɔǝɟɟǝ ʇuɐɔᴉɟᴉuᵷᴉs ɐ ǝʌɐɥ ʎɐɯ ǝʇʇǝuᵷᴉʌ ǝɥ⟘
of.options.VIGNETTE.tooltip.3=˙uǝǝɹɔsꞁꞁnɟ ᵷuᴉʎɐꞁd uǝɥʍ ʎꞁꞁɐᴉɔǝdsǝ
of.options.VIGNETTE.tooltip.2=ʎꞁǝɟɐs uɐɔ puɐ ǝꞁʇqns ʎɹǝʌ sᴉ ʇɔǝɟɟǝ ǝʇʇǝuᵷᴉʌ ǝɥ⟘
of.options.VIGNETTE.tooltip.1=˙pǝꞁqɐsᴉp ǝq

of.options.DYNAMIC_FOV=ΛOℲ ɔᴉɯɐuʎᗡ
of.options.DYNAMIC_FOV.tooltip.5=ΛOℲ ɔᴉɯɐuʎᗡ
of.options.DYNAMIC_FOV.tooltip.4=(ʇꞁnɐɟǝp) ΛOℲ ɔᴉɯɐuʎp ǝꞁqɐuǝ - NO  
of.options.DYNAMIC_FOV.tooltip.3=ΛOℲ ɔᴉɯɐuʎp ǝꞁqɐsᴉp - ℲℲO  
of.options.DYNAMIC_FOV.tooltip.2=ᵷuᴉʇuᴉɹds 'ᵷuᴉʎꞁɟ uǝɥʍ (ΛOℲ) ʍǝᴉʌ ɟo pꞁǝᴉɟ ǝɥʇ sǝᵷuɐɥƆ
of.options.DYNAMIC_FOV.tooltip.1=˙ʍoq ɐ ᵷuᴉꞁꞁnd ɹo

of.options.DYNAMIC_LIGHTS=sʇɥᵷᴉꞀ ɔᴉɯɐuʎᗡ
of.options.DYNAMIC_LIGHTS.tooltip.7=sʇɥᵷᴉꞀ ɔᴉɯɐuʎᗡ
of.options.DYNAMIC_LIGHTS.tooltip.6=(ʇꞁnɐɟǝp) sʇɥᵷᴉꞁ ɔᴉɯɐuʎp ou - ℲℲO  
of.options.DYNAMIC_LIGHTS.tooltip.5=(sɯ00ϛ ʎɹǝʌǝ pǝʇɐpdn) sʇɥᵷᴉꞁ ɔᴉɯɐuʎp ʇsɐɟ - ʇsɐℲ  
of.options.DYNAMIC_LIGHTS.tooltip.4=(ǝɯᴉʇ-ꞁɐǝɹ uᴉ pǝʇɐpdn) sʇɥᵷᴉꞁ ɔᴉɯɐuʎp ʎɔuɐɟ - ʎɔuɐℲ  
of.options.DYNAMIC_LIGHTS.tooltip.3=(˙ɔʇǝ 'ǝuoʇsʍoꞁᵷ 'ɥɔɹoʇ) sɯǝʇᴉ ᵷuᴉʇʇᴉɯǝ ʇɥᵷᴉꞁ sǝꞁqɐuƎ
of.options.DYNAMIC_LIGHTS.tooltip.2='puɐɥ uᴉ pꞁǝɥ uǝɥʍ ɯǝɥʇ punoɹɐ ᵷuᴉɥʇʎɹǝʌǝ ǝʇɐuᴉɯnꞁꞁᴉ oʇ
of.options.DYNAMIC_LIGHTS.tooltip.1=˙punoɹᵷ ǝɥʇ uo pǝddoɹp ɹo ɹǝʎɐꞁd ɹǝɥʇo ʎq pǝddᴉnbǝ

options.biomeBlendRadius.tooltip.6=sǝɯoᴉq uǝǝʍʇǝq uoᴉʇᴉsuɐɹʇ ɹnoꞁoɔ ǝɥʇ sɥʇooɯS
options.biomeBlendRadius.tooltip.5=(ʇsǝʇsɐɟ) ᵷuᴉpuǝꞁq ou - ℲℲO  
options.biomeBlendRadius.tooltip.4=(ʇꞁnɐɟǝp) ᵷuᴉpuǝꞁq ꞁɐɯɹou - ϛxϛ  
options.biomeBlendRadius.tooltip.3=(ʇsǝʍoꞁs) ᵷuᴉpuǝꞁq ꞁɐɯᴉxɐɯ - ϛ⥝xϛ⥝  
options.biomeBlendRadius.tooltip.2=sǝʞᴉds ᵷɐꞁ ʇuɐɔᴉɟᴉuᵷᴉs ǝʇɐɹǝuǝᵷ ʎɐɯ sǝnꞁɐʌ ɹǝɥᵷᴉH
options.biomeBlendRadius.tooltip.1=˙pǝǝds ᵷuᴉpɐoꞁ ʞunɥɔ ǝɥʇ uʍop ʍoꞁs puɐ

# Performance

of.options.SMOOTH_FPS=SԀℲ ɥʇooɯS
of.options.SMOOTH_FPS.tooltip.5=˙sɹǝɟɟnq ɹǝʌᴉɹp ɔᴉɥdɐɹᵷ ǝɥʇ ᵷuᴉɥsnꞁɟ ʎq SԀℲ sǝsᴉꞁᴉqɐʇS
of.options.SMOOTH_FPS.tooltip.4=ǝʇɐnʇɔnꞁɟ ʎɐɯ SԀℲ 'uoᴉʇɐsᴉꞁᴉqɐʇs ou - ℲℲO  
of.options.SMOOTH_FPS.tooltip.3=uoᴉʇɐsᴉꞁᴉqɐʇs SԀℲ - NO  
of.options.SMOOTH_FPS.tooltip.2=ʇɔǝɟɟǝ sʇᴉ puɐ ʇuɐpuǝdǝp ɹǝʌᴉɹp sɔᴉɥdɐɹᵷ sᴉ uoᴉʇdo sᴉɥ⟘
of.options.SMOOTH_FPS.tooltip.1=˙ǝꞁqᴉsᴉʌ sʎɐʍꞁɐ ʇou sᴉ

of.options.SMOOTH_WORLD=pꞁɹoM ɥʇooɯS
of.options.SMOOTH_WORLD.tooltip.5=˙ɹǝʌɹǝs ꞁɐuɹǝʇuᴉ ǝɥʇ ʎq pǝsnɐɔ sǝʞᴉds ᵷɐꞁ sǝʌoɯǝᴚ
of.options.SMOOTH_WORLD.tooltip.4=ǝʇɐnʇɔnꞁɟ ʎɐɯ SԀℲ 'uoᴉʇɐsᴉꞁᴉqɐʇs ou - ℲℲO  
of.options.SMOOTH_WORLD.tooltip.3=uoᴉʇɐsᴉꞁᴉqɐʇs SԀℲ - NO  
of.options.SMOOTH_WORLD.tooltip.2=˙pɐoꞁ ɹǝʌɹǝs ꞁɐuɹǝʇuᴉ ǝɥʇ ᵷuᴉʇnqᴉɹʇsᴉp ʎq SԀℲ sǝsᴉꞁᴉqɐʇS
of.options.SMOOTH_WORLD.tooltip.1=˙(ɹǝʎɐꞁd ǝꞁᵷuᴉs) spꞁɹoʍ ꞁɐɔoꞁ ɹoɟ ʎꞁuo ǝʌᴉʇɔǝɟɟƎ

of.options.FAST_RENDER=ɹǝpuǝᴚ ʇsɐℲ
of.options.FAST_RENDER.tooltip.6=ɹǝpuǝᴚ ʇsɐℲ
of.options.FAST_RENDER.tooltip.5=(ʇꞁnɐɟǝp) ᵷuᴉɹǝpuǝɹ pɹɐpuɐʇs - ℲℲO 
of.options.FAST_RENDER.tooltip.4=(ɹǝʇsɐɟ) ᵷuᴉɹǝpuǝɹ pǝsᴉɯᴉʇdo - NO 
of.options.FAST_RENDER.tooltip.3=sǝsɐǝɹɔǝp ɥɔᴉɥʍ ɯɥʇᴉɹoᵷꞁɐ ᵷuᴉɹǝpuǝɹ pǝsᴉɯᴉʇdo sǝs∩
of.options.FAST_RENDER.tooltip.2=˙SԀℲ ǝɥʇ ǝsɐǝɹɔuᴉ ʎꞁꞁɐᴉʇuɐʇsqns ʎɐɯ puɐ pɐoꞁ ∩ԀƆ ǝɥʇ
of.options.FAST_RENDER.tooltip.1=˙spoɯ ǝɯos ɥʇᴉʍ ʇɔᴉꞁɟuoɔ uɐɔ uoᴉʇdo sᴉɥ⟘

of.options.FAST_MATH=sɥʇɐW ʇsɐℲ
of.options.FAST_MATH.tooltip.6=sɥʇɐW ʇsɐℲ
of.options.FAST_MATH.tooltip.5=(ʇꞁnɐɟǝp) sɥʇɐɯ pɹɐpuɐʇs - ℲℲO 
of.options.FAST_MATH.tooltip.4=sɥʇɐɯ ɹǝʇsɐɟ - NO 
of.options.FAST_MATH.tooltip.3=uɐɔ ɥɔᴉɥʍ suoᴉʇɔunɟ ()soɔ puɐ ()uᴉs pǝsᴉɯᴉʇdo sǝs∩
of.options.FAST_MATH.tooltip.2=˙SԀℲ ǝɥʇ ǝsɐǝɹɔuᴉ puɐ ǝɥɔɐɔ ∩ԀƆ ǝɥʇ ǝsᴉꞁᴉʇn ɹǝʇʇǝq
of.options.FAST_MATH.tooltip.1=˙uoᴉʇɐɹǝuǝᵷ pꞁɹoʍ ǝɥʇ ʇɔǝɟɟɐ ʎꞁꞁɐɯᴉuᴉɯ uɐɔ uoᴉʇdo sᴉɥ⟘

of.options.CHUNK_UPDATES=sǝʇɐpd∩ ʞunɥƆ
of.options.CHUNK_UPDATES.tooltip.6=sǝʇɐpdn ʞunɥƆ
of.options.CHUNK_UPDATES.tooltip.5=(ʇꞁnɐɟǝp) SԀℲ ɹǝɥᵷᴉɥ 'ᵷuᴉpɐoꞁ pꞁɹoʍ ɹǝʍoꞁs - ⥝ 
of.options.CHUNK_UPDATES.tooltip.4=SԀℲ ɹǝʍoꞁ 'ᵷuᴉpɐoꞁ pꞁɹoʍ ɹǝʇsɐɟ - Ɛ 
of.options.CHUNK_UPDATES.tooltip.3=SԀℲ ʇsǝʍoꞁ 'ᵷuᴉpɐoꞁ pꞁɹoʍ ʇsǝʇsɐɟ - ϛ 
of.options.CHUNK_UPDATES.tooltip.2='ǝɯɐɹɟ pǝɹǝpuǝɹ ɹǝd sǝʇɐpdn ʞunɥɔ ɟo ɹǝqɯnN
of.options.CHUNK_UPDATES.tooltip.1=˙ǝʇɐɹǝɯɐɹɟ ǝɥʇ ǝsᴉꞁᴉqɐʇsǝp ʎɐɯ sǝnꞁɐʌ ɹǝɥᵷᴉɥ

of.options.CHUNK_UPDATES_DYNAMIC=sǝʇɐpd∩ ɔᴉɯɐuʎᗡ
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.5=sǝʇɐpdn ʞunɥɔ ɔᴉɯɐuʎᗡ
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.4=(ʇꞁnɐɟǝp) ǝɯɐɹɟ ɹǝd sǝʇɐpdn ʞunɥɔ pɹɐpuɐʇs - ℲℲO 
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.3=ꞁꞁᴉʇs ᵷuᴉpuɐʇs sᴉ ɹǝʎɐꞁd ǝɥʇ ǝꞁᴉɥʍ sǝʇɐpdn ǝɹoɯ - NO 
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.2=ǝꞁᴉɥʍ sǝʇɐpdn ʞunɥɔ ǝɹoɯ ǝɔɹoɟ sǝʇɐpdn ɔᴉɯɐuʎᗡ
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.1=˙ɹǝʇsɐɟ pꞁɹoʍ ǝɥʇ pɐoꞁ oʇ ꞁꞁᴉʇs ᵷuᴉpuɐʇs sᴉ ɹǝʎɐꞁd ǝɥʇ

of.options.LAZY_CHUNK_LOADING=ᵷuᴉpɐoꞀ ʞunɥƆ ʎzɐꞀ
of.options.LAZY_CHUNK_LOADING.tooltip.7=ᵷuᴉpɐoꞀ ʞunɥƆ ʎzɐꞀ
of.options.LAZY_CHUNK_LOADING.tooltip.6=ᵷuᴉpɐoꞁ ʞunɥɔ ɹǝʌɹǝs ʇꞁnɐɟǝp - ℲℲO 
of.options.LAZY_CHUNK_LOADING.tooltip.5=(ɹǝɥʇooɯs) ᵷuᴉpɐoꞁ ʞunɥɔ ɹǝʌɹǝs ʎzɐꞁ - NO 
of.options.LAZY_CHUNK_LOADING.tooltip.4=ʎq ᵷuᴉpɐoꞁ ʞunɥɔ ɹǝʌɹǝs pǝʇɐɹᵷǝʇuᴉ ǝɥʇ sɥʇooɯS
of.options.LAZY_CHUNK_LOADING.tooltip.3=˙sʞɔᴉʇ ꞁɐɹǝʌǝs ɹǝʌo sʞunɥɔ ǝɥʇ ᵷuᴉʇnqᴉɹʇsᴉp
of.options.LAZY_CHUNK_LOADING.tooltip.2=˙ʎꞁʇɔǝɹɹoɔ pɐoꞁ ʇou op pꞁɹoʍ ǝɥʇ ɟo sʇɹɐd ɟᴉ ℲℲO ʇᴉ uɹn⟘
of.options.LAZY_CHUNK_LOADING.tooltip.1=˙(ɹǝʎɐꞁd-ǝꞁᵷuᴉs) spꞁɹoʍ ꞁɐɔoꞁ ɹoɟ ʎꞁuo ǝʌᴉʇɔǝɟɟƎ

of.options.RENDER_REGIONS=suoᴉᵷǝᴚ ɹǝpuǝᴚ
of.options.RENDER_REGIONS.tooltip.6=suoᴉᵷǝᴚ ɹǝpuǝᴚ
of.options.RENDER_REGIONS.tooltip.5=(ʇꞁnɐɟǝp) ᵷuᴉɹǝpuǝɹ ɐꞁꞁᴉuɐʌ - ℲℲO 
of.options.RENDER_REGIONS.tooltip.4=(ɹǝʇsɐɟ) suoᴉᵷǝɹ ɹǝpuǝɹ ǝsn - NO 
of.options.RENDER_REGIONS.tooltip.3=ᵷuᴉsᴉɯᴉʇdo ʎq ᵷuᴉɹǝpuǝɹ uᴉɐɹɹǝʇ ɹǝʇsɐɟ sʍoꞁꞁⱯ
of.options.RENDER_REGIONS.tooltip.2=˙sǝɔuɐʇsᴉp ɹǝpuǝɹ ɹǝɥᵷᴉɥ ʇɐ ǝʌᴉʇɔǝɟɟǝ ǝɹoW ˙pɐoꞁ ∩Ԁ⅁ ǝɥʇ
of.options.RENDER_REGIONS.tooltip.1=˙spɹɐɔ sɔᴉɥdɐɹᵷ pǝʇɐɹᵷǝʇuᴉ ɹoɟ pǝpuǝɯɯoɔǝɹ ʇoN

of.options.SMART_ANIMATIONS=suoᴉʇɐɯᴉuⱯ ʇɹɐɯS
of.options.SMART_ANIMATIONS.tooltip.7=suoᴉʇɐɯᴉuⱯ ʇɹɐɯS
of.options.SMART_ANIMATIONS.tooltip.6=(ʇꞁnɐɟǝp) suoᴉʇɐɯᴉuɐ ʇɹɐɯs ǝsn ʇou op - ℲℲO 
of.options.SMART_ANIMATIONS.tooltip.5=(ɹǝʇsɐɟ) suoᴉʇɐɯᴉuɐ ʇɹɐɯs ǝsn - NO 
of.options.SMART_ANIMATIONS.tooltip.4=ǝɥʇ ǝʇɐɯᴉuɐ ʎꞁuo ꞁꞁᴉʍ ǝɯɐᵷ ǝɥʇ suoᴉʇɐɯᴉuɐ ʇɹɐɯs ɥʇᴉM
of.options.SMART_ANIMATIONS.tooltip.3=˙uǝǝɹɔs ǝɥʇ uo ǝꞁqᴉsᴉʌ ʎꞁʇuǝɹɹnɔ ǝɹɐ ɥɔᴉɥʍ sǝɹnʇxǝʇ
of.options.SMART_ANIMATIONS.tooltip.2=˙SԀℲ ǝɥʇ sǝsɐǝɹɔuᴉ puɐ sǝʞᴉds ᵷɐꞁ ʞɔᴉʇ ǝɥʇ sǝɔnpǝɹ sᴉɥ⟘
of.options.SMART_ANIMATIONS.tooltip.1=˙sʞɔɐd ǝɔɹnosǝɹ ᗡH puɐ sʞɔɐd poɯ ᵷᴉq ɹoɟ ꞁnɟǝsn ʎꞁꞁɐᴉɔǝdsƎ

# Animations

of.options.animation.allOn=NO ꞁꞁⱯ
of.options.animation.allOff=ℲℲO ꞁꞁⱯ
of.options.animation.dynamic=ɔᴉɯɐuʎᗡ

of.options.ANIMATED_WATER=pǝʇɐɯᴉuⱯ ɹǝʇɐM
of.options.ANIMATED_LAVA=pǝʇɐɯᴉuⱯ ɐʌɐꞀ
of.options.ANIMATED_FIRE=pǝʇɐɯᴉuⱯ ǝɹᴉℲ
of.options.ANIMATED_PORTAL=pǝʇɐɯᴉuⱯ ꞁɐʇɹoԀ
of.options.ANIMATED_REDSTONE=pǝʇɐɯᴉuⱯ ǝuoʇspǝᴚ
of.options.ANIMATED_EXPLOSION=pǝʇɐɯᴉuⱯ uoᴉsoꞁdxƎ
of.options.ANIMATED_FLAME=pǝʇɐɯᴉuⱯ ǝɯɐꞁℲ
of.options.ANIMATED_SMOKE=pǝʇɐɯᴉuⱯ ǝʞoɯS
of.options.VOID_PARTICLES=sǝꞁɔᴉʇɹɐԀ pᴉoΛ
of.options.WATER_PARTICLES=sǝꞁɔᴉʇɹɐԀ ɹǝʇɐM
of.options.RAIN_SPLASH=ɥsɐꞁdS uᴉɐᴚ
of.options.PORTAL_PARTICLES=sǝꞁɔᴉʇɹɐԀ ꞁɐʇɹoԀ
of.options.POTION_PARTICLES=sǝꞁɔᴉʇɹɐԀ uoᴉʇoԀ
of.options.DRIPPING_WATER_LAVA=ɐʌɐꞀ/ɹǝʇɐM ᵷuᴉddᴉɹᗡ
of.options.ANIMATED_TERRAIN=pǝʇɐɯᴉuⱯ uᴉɐɹɹǝ⟘
of.options.ANIMATED_TEXTURES=pǝʇɐɯᴉuⱯ sǝɹnʇxǝ⟘
of.options.FIREWORK_PARTICLES=sǝꞁɔᴉʇɹɐԀ ʞɹoʍǝɹᴉℲ

# Other

of.options.LAGOMETER=ɹǝʇǝɯoᵷɐꞀ
of.options.LAGOMETER.tooltip.8=˙(ƐℲ) uǝǝɹɔs ᵷnqǝp ǝɥʇ uo ɹǝʇǝɯoᵷɐꞁ ǝɥʇ sʍoɥS
of.options.LAGOMETER.tooltip.7=uoᴉʇɔǝꞁꞁoɔ ǝᵷɐqɹɐᵷ ʎɹoɯǝW - ǝᵷuɐɹO *
of.options.LAGOMETER.tooltip.6=ʞɔᴉ⟘ - uɐʎƆ *
of.options.LAGOMETER.tooltip.5=sǝꞁqɐʇnɔǝxǝ pǝꞁnpǝɥɔS - ǝnꞁᗺ *
of.options.LAGOMETER.tooltip.4=pɐoꞁdn ʞunɥƆ - ǝꞁdɹnԀ *
of.options.LAGOMETER.tooltip.3=sǝʇɐpdn ʞunɥƆ - pǝᴚ *
of.options.LAGOMETER.tooltip.2=ʞɔǝɥɔ ʎʇᴉꞁᴉqᴉsᴉΛ - ʍoꞁꞁǝ⅄ *
of.options.LAGOMETER.tooltip.1=uᴉɐɹɹǝʇ ɹǝpuǝᴚ - uǝǝɹ⅁ *

of.options.PROFILER=ɹǝꞁᴉɟoɹԀ ᵷnqǝᗡ
of.options.PROFILER.tooltip.5=ɹǝꞁᴉɟoɹԀ ᵷnqǝᗡ
of.options.PROFILER.tooltip.4=ɹǝʍoꞁs 'ǝʌᴉʇɔɐ sᴉ ɹǝꞁᴉɟoɹd ᵷnqǝp - NO  
of.options.PROFILER.tooltip.3=ɹǝʇsɐɟ 'ǝʌᴉʇɔɐ ʇou sᴉ ɹǝꞁᴉɟoɹd ᵷnqǝp - ℲℲO  
of.options.PROFILER.tooltip.2=uoᴉʇɐɯɹoɟuᴉ ᵷnqǝp sʍoɥs puɐ sʇɔǝꞁꞁoɔ ɹǝꞁᴉɟoɹd ᵷnqǝp ǝɥ⟘
of.options.PROFILER.tooltip.1=˙(ƐℲ) uǝdo sᴉ uǝǝɹɔs ᵷnqǝp ǝɥʇ uǝɥʍ

of.options.WEATHER=ɹǝɥʇɐǝM
of.options.WEATHER.tooltip.5=ɹǝɥʇɐǝM
of.options.WEATHER.tooltip.4=ɹǝʍoꞁs 'ǝʌᴉʇɔɐ sᴉ ɹǝɥʇɐǝʍ - NO  
of.options.WEATHER.tooltip.3=ɹǝʇsɐɟ 'ǝʌᴉʇɔɐ ʇou sᴉ ɹǝɥʇɐǝʍ - ℲℲO  
of.options.WEATHER.tooltip.2=˙sɯɹoʇsɹǝpunɥʇ puɐ ʍous 'uᴉɐɹ sꞁoɹʇuoɔ ɹǝɥʇɐǝʍ ǝɥ⟘
of.options.WEATHER.tooltip.1=˙spꞁɹoʍ ꞁɐɔoꞁ ɹoɟ ǝꞁqᴉssod ʎꞁuo sᴉ ꞁoɹʇuoɔ ɹǝɥʇɐǝM

of.options.time.dayOnly=ʎꞁuO ʎɐᗡ
of.options.time.nightOnly=ʎꞁuO ʇɥᵷᴉN

of.options.TIME=ǝɯᴉ⟘
of.options.TIME.tooltip.6=ǝɯᴉ⟘
of.options.TIME.tooltip.5=sǝꞁɔʎɔ ʇɥᵷᴉu/ʎɐp ꞁɐɯɹou - ʇꞁnɐɟǝᗡ 
of.options.TIME.tooltip.4=ʎꞁuo ʎɐp - ʎꞁuO ʎɐᗡ 
of.options.TIME.tooltip.3=ʎꞁuo ʇɥᵷᴉu - ʎꞁuO ʇɥᵷᴉN 
of.options.TIME.tooltip.2=ǝpoɯ ƎΛI⟘ⱯƎᴚƆ uᴉ ǝʌᴉʇɔǝɟɟǝ ʎꞁuo sᴉ ᵷuᴉʇʇǝs ǝɯᴉʇ ǝɥ⟘
of.options.TIME.tooltip.1=˙spꞁɹoʍ ꞁɐɔoꞁ ɹoɟ puɐ

options.fullscreen.tooltip.5=uǝǝɹɔsꞁꞁnℲ
options.fullscreen.tooltip.4=ǝpoɯ uǝǝɹɔsꞁꞁnɟ ǝsn - NO  
options.fullscreen.tooltip.3=ǝpoɯ ʍopuᴉʍ ǝsn - ℲℲO  
options.fullscreen.tooltip.2=uɐɥʇ ɹǝʍoꞁs ɹo ɹǝʇsɐɟ ǝq ʎɐɯ ǝpoɯ uǝǝɹɔsꞁꞁnℲ
options.fullscreen.tooltip.1=˙pɹɐɔ sɔᴉɥdɐɹᵷ ǝɥʇ uo ᵷuᴉpuǝdǝp 'ǝpoɯ ʍopuᴉʍ

options.fullscreen.resolution=ǝpoW uǝǝɹɔsꞁꞁnℲ
options.fullscreen.resolution.tooltip.5=ǝpoɯ uǝǝɹɔsꞁꞁnℲ
options.fullscreen.resolution.tooltip.4=ɹǝʍoꞁs 'uoᴉʇnꞁosǝɹ uǝǝɹɔs doʇʞsǝp ǝsn - ʇꞁnɐɟǝᗡ  
options.fullscreen.resolution.tooltip.3=ɹǝʇsɐɟ ǝq ʎɐɯ 'uoᴉʇnꞁosǝɹ uǝǝɹɔs ɯoʇsnɔ ǝsn - HxM  
options.fullscreen.resolution.tooltip.2=˙(⥝⥝Ⅎ) ǝpoɯ uǝǝɹɔsꞁꞁnɟ uᴉ pǝsn sᴉ uoᴉʇnꞁosǝɹ pǝʇɔǝꞁǝs ǝɥ⟘
options.fullscreen.resolution.tooltip.1=˙ɹǝʇsɐɟ ǝq ʎꞁꞁɐɹǝuǝᵷ pꞁnoɥs suoᴉʇnꞁosǝɹ ɹǝʍoꞀ

of.options.SHOW_FPS=SԀℲ ʍoɥS
of.options.SHOW_FPS.tooltip.7=˙uoᴉʇɐɯɹoɟuᴉ ɹǝpuǝɹ puɐ SԀℲ ʇɔɐdɯoɔ sʍoɥS
of.options.SHOW_FPS.tooltip.6=ɯnɯᴉuᴉɯ/ǝᵷɐɹǝʌɐ - sdℲ  
of.options.SHOW_FPS.tooltip.5=sɹǝɹǝpuǝɹ ʞunɥɔ - :Ɔ  
of.options.SHOW_FPS.tooltip.4=sǝᴉʇᴉʇuǝ ʞɔoꞁq + sǝᴉʇᴉʇuǝ pǝɹǝpuǝɹ - :Ǝ  
of.options.SHOW_FPS.tooltip.3=sǝʇɐpdn ʞunɥɔ - :∩  
of.options.SHOW_FPS.tooltip.2=ǝɥʇ uǝɥʍ uʍoɥs ʎꞁuo sᴉ uoᴉʇɐɯɹoɟuᴉ SԀℲ ʇɔɐdɯoɔ ǝɥ⟘
of.options.SHOW_FPS.tooltip.1=˙ǝꞁqᴉsᴉʌ ʇou sᴉ uǝǝɹɔs ᵷnqǝp

of.options.save.45s=sϛ߈
of.options.save.90s=s06
of.options.save.3min=uᴉɯƐ
of.options.save.6min=uᴉɯ9
of.options.save.12min=uᴉɯᘔ⥝
of.options.save.24min=uᴉɯ߈ᘔ

of.options.AUTOSAVE_TICKS=ǝʌɐsoʇnⱯ
of.options.AUTOSAVE_TICKS.tooltip.4=ꞁɐʌɹǝʇuI ǝʌɐsoʇnⱯ
of.options.AUTOSAVE_TICKS.tooltip.3=ʇꞁnɐɟǝp - sϛ߈ 
of.options.AUTOSAVE_TICKS.tooltip.2=˙ǝɔuɐʇsᴉp ɹǝpuǝɹ ǝɥʇ uo ᵷuᴉpuǝdǝp sǝʞᴉds ᵷɐꞁ ǝʇɐɹǝuǝᵷ ʎɐɯ ǝʌɐsoʇnⱯ
of.options.AUTOSAVE_TICKS.tooltip.1=˙pǝuǝdo sᴉ nuǝɯ ǝɯɐᵷ ǝɥʇ uǝɥʍ pǝʌɐs osꞁɐ sᴉ pꞁɹoʍ ǝɥ⟘

of.options.SCREENSHOT_SIZE=ǝzᴉS ʇoɥsuǝǝɹɔS
of.options.SCREENSHOT_SIZE.tooltip.6=ǝzᴉS ʇoɥsuǝǝɹɔS
of.options.SCREENSHOT_SIZE.tooltip.5=ǝzᴉs ʇoɥsuǝǝɹɔs ʇꞁnɐɟǝp - ʇꞁnɐɟǝᗡ  
of.options.SCREENSHOT_SIZE.tooltip.4=ǝzᴉs ʇoɥsuǝǝɹɔs ɯoʇsnɔ - x߈-xᘔ  
of.options.SCREENSHOT_SIZE.tooltip.3=˙ʎɹoɯǝɯ ǝɹoɯ pǝǝu ʎɐɯ sʇoɥsuǝǝɹɔs ɹǝᵷᵷᴉq ᵷuᴉɹnʇdɐƆ
of.options.SCREENSHOT_SIZE.tooltip.2=˙ᵷuᴉsɐᴉꞁɐᴉʇuⱯ ɥʇᴉʍ ǝꞁqᴉʇɐdɯoɔ ʇoN
of.options.SCREENSHOT_SIZE.tooltip.1=˙ʇɹoddns ɹǝɟɟnqǝɯɐɹɟ ∩Ԁ⅁ sǝɹᴉnbǝᴚ

of.options.SHOW_GL_ERRORS=sɹoɹɹƎ Ꞁ⅁ ʍoɥS
of.options.SHOW_GL_ERRORS.tooltip.6=sɹoɹɹƎ Ꞁ⅁uǝdO ʍoɥS
of.options.SHOW_GL_ERRORS.tooltip.5=˙ʇɐɥɔ ǝɥʇ uᴉ uʍoɥs ǝɹɐ sɹoɹɹǝ Ꞁ⅁uǝdO pǝꞁqɐuǝ uǝɥM
of.options.SHOW_GL_ERRORS.tooltip.4=puɐ ʇɔᴉꞁɟuoɔ uʍouʞ ɐ sᴉ ǝɹǝɥʇ ɟᴉ ʎꞁuo ʇᴉ ǝꞁqɐsᴉᗡ
of.options.SHOW_GL_ERRORS.tooltip.3=˙pǝxᴉɟ ǝq ʇ,uɐɔ sɹoɹɹǝ ǝɥʇ
of.options.SHOW_GL_ERRORS.tooltip.2=ǝɥʇ uᴉ pǝᵷᵷoꞁ ꞁꞁᴉʇs ǝɹɐ sɹoɹɹǝ ǝɥʇ pǝꞁqɐsᴉp uǝɥM
of.options.SHOW_GL_ERRORS.tooltip.1=˙doɹp SԀℲ ʇuɐɔᴉɟᴉuᵷᴉs ɐ ǝsnɐɔ ꞁꞁᴉʇs ʎɐɯ ʎǝɥʇ puɐ ᵷoꞁ ɹoɹɹǝ

# Chat Settings

of.options.CHAT_BACKGROUND=punoɹᵷʞɔɐᗺ ʇɐɥƆ
of.options.CHAT_BACKGROUND.tooltip.4=punoɹᵷʞɔɐᗺ ʇɐɥƆ
of.options.CHAT_BACKGROUND.tooltip.3=ɥʇpᴉʍ pǝxᴉɟ - ʇꞁnɐɟǝᗡ  
of.options.CHAT_BACKGROUND.tooltip.2=ɥʇpᴉʍ ǝuᴉꞁ sǝɥɔʇɐɯ - ʇɔɐdɯoƆ  
of.options.CHAT_BACKGROUND.tooltip.1=uǝppᴉɥ - ℲℲO  

of.options.CHAT_SHADOW=ʍopɐɥS ʇɐɥƆ
of.options.CHAT_SHADOW.tooltip.3=ʍopɐɥS ʇɐɥƆ
of.options.CHAT_SHADOW.tooltip.2=ʍopɐɥs ʇxǝʇ ǝsn - NO  
of.options.CHAT_SHADOW.tooltip.1=ʍopɐɥs ʇxǝʇ ou - ℲℲO  
