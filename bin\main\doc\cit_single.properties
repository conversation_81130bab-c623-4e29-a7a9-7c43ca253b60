###############################################################################
# Sample configuration for OptiFine's Custom Items feature.
# Based on the configuration for MCPatcher's Custom Item Textures mod.
# Reference: https://bitbucket.org/prupe/mcpatcher/wiki/Custom_Item_Textures
#
###############################################################################
# cit_single.properties
###############################################################################
# Sample properties file for use with <PERSON>P<PERSON><PERSON>'s Custom Item Textures mod.
#
# This file is offered without any copyright restrictions. Please copy and
# modify it to suit your needs.
#
# For each item you with to override with a custom texture, use this template
# and create a .properties file in the mcpatcher/cit folder of your texture
# pack.  Properties files can be organized into subfolders of any depth, as
# long as everything is within the top-level mcpatcher/cit folder.
#
# Each properties file specifies a list of matching item IDs or names, a
# replacement texture, and an optional set of rules specifying damage, stack
# size, or NBT tags.
#
# All property names are case-sensitive.
# All paths are relative to assets/minecraft unless otherwise stated.
###############################################################################

###############################################################################
# General properties used by all types:
###############################################################################

# (Optional) Type of texture replacement.
# item:        Simple item texture replacement.  Applies to items in GUI, held
#              in hand, and in the world.  If multiple properties files match
#              the same item, only the first (sorted by weight, then by
#              filename) is used.
# enchantment: Overlay texture for enchantments (replaces misc/glint.png).  If
#              multiple properties files match the same item, they are blended
#              together using rules specified in the global cit.properties
#              file.
# armor:       Armor texture replacement.  Applies to armor models worn by
#              players and mobs.  If multiple properties files match the same
#              item, only the first (sorted by weight, then by filename) is
#              used.
# The default type is item.  Each type has additional properties defined in
# later sections of this template.
type=<item | enchantment | armor>

# (Optional for type=enchantment, required for other types) List of items to
# apply the replacement texture to.
items=<list of item IDs>

# (Optional) Replacement texture.  Can be a full path or just a name:
# mytextures/excalibur.png -> mytextures/excalibur.png
# excalibur                -> mcpatcher/cit/excalibur.png
# Texture format including animation is handled differently depending on the
# type.  See the type-specific sections below.
#
# If no texture is provided, MCPatcher will use the name of the properties file,
# mcpatcher/cit/excalibur.properties -> mcpatcher/cit/excalibur.png
texture=<replacement texture>

# (Optional) Replacement model.
# A json item model in vanilla format (http://minecraft.gamepedia.com/Model#Item_models)
# item/mymodel -> /assets/minecraft/models/item/mymodel.json
# ./mymodel    -> mymodel.json from the same folder as the properties file
# The model may reference textures from the same folder, for example: "./mytexture"
model=<replacement model>  

# (Optional) List of damage values.  Use the replacement texture only when the
# item damage is a certain value or range.
#
# For items with durability, damage starts at 0 for a new item and increases as
# it gets damaged.  The max damage an item can take varies, see
# http://www.minecraftwiki.net/wiki/Item_durability
#
# For other items, damage represents different properties like potion type or
# wool color.  See http://www.minecraftwiki.net/wiki/Data_values for specifics.
damage=<damage values 0-65535>
# Damage can also be given as a percentage: 
# damage=0-50%
#
# An optional bitmask applied to the item's damage before checking it against
# the list of eligible damage values.  Examples,
# Match any Fire Resistance potion:
#   damage=3
#   damageMask=15
# Match any non-splash Fire Resistance potion:
#   damage=3
#   damageMask=16399
# Match non-splash Fire Resistance I potion only:
#   damage=3
#   damageMask=16447
# Match splash Fire Resistance II potion only:
#   damage=16403
#   damageMask=16447
# For a simpler way to do potions, see the section at the end of this file.
damageMask=<bitmask>

# (Optional) List of stack sizes.  Use the replacement texture only when the
# stack size is a certain value or range.
stackSize=<stack sizes 0-65535>

# (Optional) List of enchantment IDs or names.
# The enchantment names may be short ("flame") or full ("minecraft:flame").
# For reference, the vanilla enchantment IDs are
# 0:  Protection
# 1:  Fire Protection
# 2:  Feather Falling
# 3:  Blast Protection
# 4:  Projectile Protection
# 5:  Respiration
# 6:  Aqua Affinity
# 7:  Thorns
# 16: Sharpness
# 17: Smite
# 18: Bane of Arthropods
# 19: Knockback
# 20: Fire Aspect
# 21: Looting
# 32: Efficiency
# 33: Silk Touch
# 34: Unbreaking
# 35: Fortune
# 48: Power
# 49: Punch
# 50: Flame
# 51: Infinity
enchantmentIDs=<enchantment IDs 0-255 or names>

# (Optional) List of enchantment levels.
enchantmentLevels=<enchantment levels 0-255>
# Examples:
# Match Silk Touch, any level:
#   enchantmentIDs=33
# Match Flame or Fire Aspect, level 3 or higher:
#   enchantmentIDs=flame minecraft:fire_aspect
#   enchantmentLevels=3-
# Match any enchantment, with 8-10 total levels across all enchantments:
#   enchantmentLevels=8-10

# (Optional) NBT-based rule.  Use the replacement texture only when an NBT tag
# has a specific value.  If multiple rules are provided, all of them must
# match.  Use a utility like NBTExplorer to view the NBT tags for various
# items.
nbt.<tag>=<value>
# Currently, only the following NBT types are supported:
# - String, Integer, Short, Long, Double, Float - match exact value only
# - Compound - Can match a specific tag or any tag (*).
# - List - Can match a specific index (starting at 0) or any index (*).
# A value starting with "!" performs a negative match (not).
# Integer values can also be specified as hex color, for example #ff99cc.
# The list size is named "count". 
#
# Examples:
# Match item display name:
#   nbt.display.Name=My Sword
#
# Match item display name with special formatting:
# NOTE: For best compatibility, use the escape sequence '\u00a7' instead of §:
#   nbt.display.Name=\u00a74\u00a7oMy Sword
#
# Match item lore (first line only):
#   nbt.display.Lore.0=My Lore Text
#
# Match item lore (any line):
#   nbt.display.Lore.*=My Lore Text
#
# Strings can be matched in several ways:
# - Exact value
#   nbt.display.Name=Letter to Herobrine
# Matches the exact string "Letter to Herobrine" and nothing else.
#
# - Wildcards using ? and *
#   nbt.display.Name=pattern:Letter to *
# Matches
#   Letter to Herobrine
#   Letter to a creeper
# but not
#   letter to Herobrine
#
# - Wildcards, case insensitive
#   nbt.display.Name=ipattern:Letter to *
# Matches
#   Letter to Herobrine
#   Letter to a creeper
#   letter to Herobrine
#
# - Java regular expressions
#   (See http://docs.oracle.com/javase/6/docs/api/java/util/regex/Pattern.html)
#   nbt.display.Name=regex:Letter (to|from) .*
# Matches
#   Letter to Herobrine
#   Letter from Herobrine
# but not
#   letter to Herobrine
#   A Letter to Herobrine
#
# - Java regular expressions, case insensitive
#   nbt.display.Name=iregex:Letter (to|from) .*
# Matches
#   Letter to Herobrine
#   Letter from Herobrine
#   letter to Herobrine
# but not
#   A Letter to Herobrine
#
# NOTE: Any backslashes must be doubled as well.  Literal backslashes within a
# regular expression or wildcard must be quadrupled.
# Correct:
#   nbt.display.name=regex:\\d+
#   nbt.display.name=regex:\\\\
#   nbt.display.name=/\\/\\
# Wrong:
#   nbt.display.name=regex:\d+
#   nbt.display.name=regex:\\
#   nbt.display.name=/\/\
#
# You can match potions with custom effects using "damage=0" and NBT rules:
#   items=potion
#   damage=0
#   nbt.CustomPotionEffects.0.Id=20
#
# To override the default potion tint, you can use a CustomPotionColor tag:
#   /give @p minecraft:potion 1 0 {CustomPotionEffects:[{Id:20,Amplifier:0,Duration:20}],CustomPotionColor:-1}
#
###############################################################################
# Type-specific properties
###############################################################################

###############################################################################
# Items
###############################################################################

type=item

# Texture format:
# Item replacement textures are stitched into items.png, and thus follow the
# same rules as normal item textures.  In particular, this means that
# animations must use Mojang's system of .mcmeta files for frame order and
# timing.
texture=<replacement texture>

# (Optional) Replacement for alternate textures.
# For items with more than one texture, this allows you to specify replacements
# for each separately.  For example, the vanilla bow has four possible textures
# depending on its state:  bow_standby, bow_pulling_0, bow_pulling_1,
# bow_pulling_2.  To replace all four, you could use
#   texture.bow_standby=my_special_bow_standby
#   texture.bow_pulling_0=my_special_bow_pulling_0
#   texture.bow_pulling_1=my_special_bow_pulling_1
#   texture.bow_pulling_2=my_special_bow_pulling_2
# Potions also have two textures.  To replace them, use
#   texture.potion_bottle_drinkable=...
#   -or-
#   texture.potion_bottle_splash=...
#   texture.potion_overlay=...
# If no texture.<name> property matches, the generic texture property is used
# instead.
texture.<name>=<replacement texture>

# (Optional) Replacement model.
# A json item model in vanilla format (http://minecraft.gamepedia.com/Model#Item_models)
# item/mymodel -> assets/minecraft/models/item/mymodel.json
# ./mymodel    -> mymodel.json from the same folder as the properties file
# The model may reference textures from the same folder, for example: "./mytexture"
model=<replacement model>  

# (Optional) Replacement for alternative models
# For items with more than one model, this allows you to specify replacements
# for each model separately, for example:
#   model.bow_standby=my_special_bow_standby
#   model.bow_pulling_0=my_special_bow_pulling_0
#   model.bow_pulling_1=my_special_bow_pulling_1
#   model.bow_pulling_2=my_special_bow_pulling_2
model.<name>=<replacement model>

# (Optional) If multiple properties files match the same item, the highest
# weighted one is used.  In the event of a tie, the properties filenames are
# compared next.  The default weight is 0.
weight=<integer>

###############################################################################
# Enchantments
###############################################################################

type=enchantment

# Texture format:
# The enchantment texture can be any resolution.  To animate an enchantment,
# use the anim/*.properties method with
#   to=<full path to enchantment texture>
texture=<enchantment texture>

# (Optional) Blend method when applying texture to the texture below it.  See
# Better Skies for a list of valid methods.  The default is add.
blend=<blend method>

# (Optional) Scrolling speed of texture.
# The default value is 0 (no scrolling).
speed=<value>

# (Optional) Angle of texture relative to the item.  If speed is non-zero, the
# texture will also scroll in this direction.
# The default value is 0.
rotation=<0-360 degrees>

# (Optional) Specifies a unique layer and the ordering of the layers as they
# overlap each other.  If two or more effects use the same layer, weight
# determines which effect is rendered (the other is not rendered).
# The default layer is 0 (bottommost).
layer=<integer>

# (Optional) Relative priority of the enchantment within a layer.
# Of the matching effects, only the highest weighted one within a layer is
# rendered.
# In other words,
# - The layer property determines the ORDER in which effects are rendered.
# - The weight property determines WHICH effect is rendered for each layer.
# If two effects have the same weight and layer, the filename is used as a
# tiebreaker.
# The default weight is 0.
weight=<integer>

# (Optional, for cycling enchantments only) Duration in seconds of enchantment
# in a cycle.
# The default is 1 second.
duration=<seconds>

###############################################################################
# Armor
###############################################################################

type=armor

# Texture format:
# The texture should match the format of the corresponding armor texture.  For
# animated textures, use the anim/*.properties method with
#   to=<path to replacement texture>

# (Required) Replacement textures.  You need a replacement for each texture in
# textures/models/armor for that armor type.
# For diamond armor,
#   texture.diamond_layer_1=my_diamond_armor_1
#   texture.diamond_layer_2=my_diamond_armor_2
# Leather armor has four layers:
#   texture.leather_layer_1=...
#   texture.leather_layer_1_overlay=...
#   texture.leather_layer_2=...
#   texture.leather_layer_2_overlay=...
# If no texture.<name> property is matching, the generic texture will be used.
texture.<name>=<replacement texture>

# (Optional) If multiple properties files match the same armor, the highest
# weighted one is used.  In the event of a tie, the properties filenames are
# compared next.  The default weight is 0.
weight=<integer>

###############################################################################
# Potions
###############################################################################

# http://www.minecraftwiki.net/wiki/Data_values#Potions
# As an alternative to listing potion damage values, you can specify
# replacement textures for potions using a filename based system.  Note that
# everything described here CAN be done via cit properties files; this is just
# a shortcut.
#
# There are two directories for potions:
#   mcpatcher/cit/potion/normal - non-splash potions
#   mcpatcher/cit/potion/splash - splash potions
#
# Within either directory, create a .png file with the name of the potion
# effect.  No properties files are necessary.
#   absorption.png
#   blindness.png
#   confusion.png
#   damageBoost.png    (*)
#   digSlowDown.png
#   digSpeed.png
#   fireResistance.png (*)
#   harm.png           (*)
#   heal.png           (*)
#   healthBoost.png
#   hunger.png
#   invisibility.png   (*)
#   jump.png
#   moveSlowdown.png   (*)
#   moveSpeed.png      (*)
#   nightVision.png    (*)
#   poison.png         (*)
#   regeneration.png   (*)
#   resistance.png
#   saturation.png
#   waterBreathing.png
#   weakness.png       (*)
#   wither.png
# The names are the same as the potion.* properties in Custom Colors'
# color.properties file.  Case matters.  Only potions marked (*) are obtainable
# in-game.  The others can only be created via editing.
#
# The replacement png will automatically be used for that potion type; no
# properties file required.  Note that this replaces BOTH
# potion.png/potion_splash.png and potion_contents.png from the standard potion
# rendering.  So be sure to include the colored liquid in the replacement art.
#
# Similarly, you can replace textures for the various "no effect" potions.
# These have non-splash versions only, and again only the ones marked (*)
# actually exist in-game.  The rest are in the code and are listed here only
# for completeness.
#   acrid.png
#   artless.png
#   awkward.png        (*)
#   bland.png
#   bulky.png
#   bungling.png
#   buttered.png
#   charming.png
#   clear.png
#   cordial.png
#   dashing.png
#   debonair.png
#   diffuse.png
#   elegant.png
#   fancy.png
#   flat.png
#   foul.png
#   gross.png
#   harsh.png
#   milky.png
#   mundane.png        (*)
#   odorless.png
#   potent.png
#   rank.png
#   refined.png
#   smooth.png
#   sparkling.png
#   stinky.png
#   suave.png
#   thick.png          (*)
#   thin.png
#   uninteresting.png
#
# If you'd rather have a single texture for all "no effect" potions, this file
# is used as a fallback for any that do not have a specific replacement as
# listed above:
#   mcpatcher/cit/potion/normal/other.png
#
# Two additional textures (non-splash only) can also be provided:
#   mcpatcher/cit/potion/normal/water.png - plain water bottle (item 373:0)
#   mcpatcher/cit/potion/normal/empty.png - empty bottle (item 374)
