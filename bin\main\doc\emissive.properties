###############################################################################
# Sample configuration for OptiFine's Emissive Textures feature.
###############################################################################
# emissive.properties
# This file should be placed in the resource pack folder "assets/minecraft/optifine" 
###############################################################################
###############################################################################
# Emissive textures
###############################################################################
# It is possible to add overlays to block textures, which will always rendered 
# with full brightness. This can simulate light emitting parts of the textures.
# The emissive overlays have the same name as the base texture + custom suffix.
# For example:
#   bedrock.png   - base texture
#   bedrock_e.png - emissive overlay
# The emissive overlays are rendered in the same block layer as the base texture,
# except overlays for textures from layer SOLID, which are rendered as CUTOUT_MIPPED.
# The overlays can also be used for items, mobs and block entities.
suffix.emissive=_e
