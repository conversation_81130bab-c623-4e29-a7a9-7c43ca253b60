###############################################################################
# Sample configuration for OptiFine's Custom Blocks feature.
###############################################################################
# block.properties
###############################################################################
# This file is offered without any copyright restrictions. 
# Please copy and modify it to suit your needs.
#
# Location: "/assets/minecraft/optifine/block.properties"
#

# Render layer (optional)
# Defines custom render layer for the given blocks
# Layers
#   solid - no alpha, no blending (solid textures)
#   cutout - alpha, no blending (cutout textures)
#   cutout_mipped - alpha, no blending, mipmaps (cutout with mipmaps)
#   translucent - alpha, blending, mipmaps (water, stained glass)
# 
# Blocks which are solid opaque cubes (stone, dirt, ores, etc) can't be rendered on a custom layer
# as this would affect face culling, ambient occlusion, light propagation and so on.
#
# For exaple:
#   layer.translucent=glass_pane fence wooden_door
# 
layer.solid=<blocks>
layer.cutout=<blocks>
layer.cutout_mipped=<blocks>
layer.translucent=<blocks>
 