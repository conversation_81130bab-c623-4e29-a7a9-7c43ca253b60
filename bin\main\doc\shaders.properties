###############################################################################
# Sample configuration for OptiFine's Shaders feature.
#
###############################################################################
# shaders.properties
###############################################################################
# Place this file in the "shaders" folder of the shader pack
# 
# This file can use conditional preprocessor directives (#ifdef, #if, etc.)
# For more details see "shaders.txt", section "Standard Macros" A to G.
# Option macros are not available.
#
# Set clouds type or turn clouds off
# The clouds are also controlled by "Video Settings -> Details -> Clouds" with higher priority
clouds=fast|fancy|off

# Enable or disable old hand light 
# Backwards compatibility for shader packs not supporting main and off hand.
# When enabled uses the handheld item with higher light value for the main hand.  
# The old hand light is also controlled by "Video Settings -> Shaders -> Old Hand Light" with higher priority
oldHandLight=true|false

# Enable or disable the dynamic hand light from "Dynamic Lights"
# This option can be used to disable the dynamic hand light from "Dynamic Lights" if the shader implements its own hand light.
dynamicHandLight=true|false

# Enable or disable old block lighting with fixed multiplier
# The old block lighting is also controlled by "Video Settings -> Shaders -> Old Lighting" with higher priority
oldLighting=true|false

# Enable or disable rendering of translucent blocks (water, stained glass) in the shadow pass
shadowTranslucent=true|false

# Enable or disable underwater screen overlay
underwaterOverlay=true|false

# Enable or disable sun rendering
sun=true|false

# Enable or disable moon rendering
moon=true|false

# Enable or disable vignette rendering
vignette=true|false

# Enable back-face rendering per render layer
# Default is false
backFace.solid=true|false 
backFace.cutout=true|false
backFace.cutoutMipped=true|false
backFace.translucent=true|false

# Rain depth
# Enables rain and snow to write to the depth buffer
rain.depth=true|false

# Beacon beam depth
# Enables beacon beam to write to the depth buffer
beacon.beam.depth=true|false

# Separate AO
# When enabled the AO brightness (smooth lighting) is separated from "color.rbg" and put in "color.a". 
separateAo=true|false

# Frustum culling
# Enable or disable frustum culling
frustum.culling=true|false

# OptiFine version
# The minimum OptiFine version which is required by the shader pack
# Each Minecraft version has to be specified separately.
# For example:
#   version.1.12.2=D1
#   version.1.10.2=F1
#   version.1.8=J1
version.<mc_ver>=<of_edition>

# Custom textures
# Allows custom textures to be bound to the available shader units.
# Format:
#  texture.<stage>.<name>=<path>
# Stage:
#  gbuffers - gbuffers and shadow programs
#  deferred - deferred programs
#  composite - composite and final programs
# Name is the texture unit name, see "shaders.txt" for the available names.
# The textures can be loaded from different places:
# 1. Shader pack 
#   The texture path is relative to the folder "shaders".
#   Example:
#     texture.composite.colortex1=textures/noise.png
# 2. Resource pack
#   The texture path should start with "minecraft:"
#   Example
#     texture.composite.colortex2=minecraft:textures/font/ascii.png
# 3. Dynamic (lightmap, texture atlas)
#   Example
#     texture.composite.colortex3=minecraft:dynamic/lightmap_1
#     texture.composite.colortex4=minecraft:textures/atlas/blocks.png
# The suffix "_n" and "_s" can be used to load the normal/specular variant of the texture,
# for example "minecraft:textures/atlas/blocks_n.png"
# 
# Raw textures (binary dump) can also be loaded:
#   texture.<stage>.<name>=<path> <type> <internalFormat> <dimensions> <pixelFormat> <pixelType>
# Where:
#   <type> is one of: TEXTURE_1D, TEXTURE_2D, TEXTURE_3D, TEXTURE_RECTANGLE
#   <internalFormat> is the texture format, see Texture Formats in "shaders.txt" for the available names
#   <dimensions> is a list of texture dimensions, depends on the texture type
#   <pixelFormat> is the pixel format, see Pixel Formats in "shaders.txt" for the available names
#   <pixelType> is the pixel type, see Pixel Types in "shaders.txt" for the available names
# For example: 
#   texture.composite.gaux1=textures/lut_1d.dat TEXTURE_1D RGBA32F 256 RGBA FLOAT
#   texture.composite.gaux1.2=textures/lut_3d.dat TEXTURE_3D RGBA32F 64 64 64 RGBA FLOAT
# It is possible to bind several textures with different types to one texture unit.
# The shaders can differentiate between them based on the sampler type: sampler1d, sampler2d, sampler3d ...
# In one program only one sampler type can be used per texture unit.
# The suffixes ".0" to ".9" can be added to <name> to avoid duplicate property keys.
# Wrap and filter modes can be configured by adding standard texture ".mcmeta" files, 
# for example: "textures/lut_3d.dat.mcmeta"
texture.<stage>.<name>=<path>

# Custom noise texture
# Allows the noise texture to be loaded from the shader pack
texture.noise=<path>

# Shader options are parsed from the ".fsh" and ".vsh" files located in the folder "shaders".
# The line comment located after the option is shown as a tooltip.
# Tooltip lines are split on sentence end ". ".
# Tooltip lines ending with "!" are automatically shown red.
# One option can be present in several shader files and it will be switched simultaneously in all of them.
# Ambiguous options (different default values found) are disabled and can not be changed.
# Left click on an option button selects the next value, right click selects the previous value and 
# Shift + click resets the option to default value.
#
# Boolean, default ON
#   #define SSAO       // Screen space ambient occlusion. High performance impact.
# Boolean, default OFF
#   // #define SSAO    // Screen space ambient occlusion. High performance impact.
#
# The boolean variables are recognized only if the matching "#ifdef" or "#ifndef" is also found in the same file. 
#
# Variable
#   #define SHADOW_DARKNESS 0.10   // Shadow darkness levels [0.05 0.10 0.20]
#
# The allowed values are given as a list "[v1 v2 v3]" in the comment.
# The default value is automatically added if not present in the list.
#
# Some "const" variables are also recognized (backwards compatibility with the Shaders Mod).
# They use a structure similar to the macro variables, for example: 
#   const int shadowMapResolution = 1572; // Shadowmap resolution [1024 1572 2048]
#   const float shadowDistance = 64.0;    // Draw distance of shadows [32.0 64.0 128.0 256.0]
#
# Const variables without allowed values are by default not visible, unless used in a profile or configured on a screen.
#
# The recognized "const" variables are:
#   shadowMapResolution
#   shadowDistance
#   shadowDistanceRenderMul
#   shadowIntervalSize
#   generateShadowMipmap
#   generateShadowColorMipmap
#   shadowHardwareFiltering
#   shadowHardwareFiltering0
#   shadowHardwareFiltering1
#   shadowtex0Mipmap
#   shadowtexMipmap
#   shadowtex1Mipmap
#   shadowcolor0Mipmap
#   shadowColor0Mipmap
#   shadowcolor1Mipmap
#   shadowColor1Mipmap
#   shadowtex0Nearest
#   shadowtexNearest
#   shadow0MinMagNearest
#   shadowtex1Nearest
#   shadow1MinMagNearest
#   shadowcolor0Nearest
#   shadowColor0Nearest
#   shadowColor0MinMagNearest
#   shadowcolor1Nearest
#   shadowColor1Nearest
#   shadowColor1MinMagNearest
#   wetnessHalflife
#   drynessHalflife
#   eyeBrightnessHalflife
#   centerDepthHalflife
#   sunPathRotation
#   ambientOcclusionLevel
#   superSamplingLevel
#   noiseTextureResolution
#
# User friendly option labels can be loaded from language files in "/shaders/lang"
# Example from "/shaders/lang/en_US.lang":
#   option.SHADOW_FILTER=Shadow Filter
#   option.SHADOW_FILTER.comment=Smooth out edges of shadows. Very small performance hit.
#
# User friendly value labels can be loaded from language files in "/shaders/lang"
# Example from "/shaders/lang/en_US.lang"
#   value.SHADOW_FILTER.0.4f=Normal
#   value.SHADOW_FILTER.0.9f=Soft
#
# Value formatting can be added with:
#   prefix.SHADOW_FILTER=(
#   suffix.SHADOW_FILTER=)
#
# Profile tooltips can be loaded from language files in "/shaders/lang"
# Example from "/shaders/lang/en_US.lang":
#   profile.comment=Low - low. Medium - medium. Standard - standard. High - high. Ultra - ultra.

# Sliders
# Options with multiple allowed values can be shown as sliders
sliders=<list of options>
 
# Profiles allow a set of options to be switched together
# The current profile is detected based on the selected option values
# If no profile matches the current option values, the profile "Custom" is selected
# It is recommended that all profiles use the same list of options and only the values differ
# Disabled programs are special options and only disabling (prefix !) is recognized for them 
profile.NAME=<list of options>

# Profile options
#   OPTION:value  - set value
#   OPTION=value  - set value
#   OPTION        - set boolean option ON
#   !OPTION       - set boolean option OFF
#   profile.NAME  - copy all options from another profile
#   !program.name - disable program "name". The program name may include dimension: "world-1/gbuffers_water"
#
# The following program names are recognized:
#   gbuffers_basic
#   gbuffers_textured
#   gbuffers_textured_lit
#   gbuffers_skybasic
#   gbuffers_skytextured
#   gbuffers_clouds
#   gbuffers_terrain
#   gbuffers_terrain_solid
#   gbuffers_terrain_cutout_mip
#   gbuffers_terrain_cutout
#   gbuffers_damagedblock
#   gbuffers_water
#   gbuffers_block
#   gbuffers_beaconbeam
#   gbuffers_item
#   gbuffers_entities
#   gbuffers_armor_glint
#   gbuffers_spidereyes
#   gbuffers_hand
#   gbuffers_weather
#   composite
#   composite1
#   composite2
#   composite3
#   composite4
#   composite5
#   composite6
#   composite7
#   final
#   shadow
#   shadow_solid
#   shadow_cutout
#   deferred
#   deferred1
#   deferred2
#   deferred3
#   deferred4
#   deferred5
#   deferred6
#   deferred7
#   gbuffers_hand_water
#
# Examples
# profile.LOW=SSAO:false GOD_RAYS:false SHADOW_DIST:40 !program.composite1
# profile.MED=profile.LOW GOD_RAYS SHADOW_DIST:80
# profile.HIGH=SSAO GOD_RAYS SHADOW_DIST:120 
# 
# User friendly profile labels can be loaded from language files in "/shaders/lang"
# Example from "/shaders/lang/en_US.lang"
# profile.LOW=Low
# profile.LOW.comment=Low quality. Intel and Mac compatible. No God Rays and SSAO.
# profile.MED=Medium
# profile.MED.comment=Medium quality. Nvidia or AMD graphics card recommended.
# profile.HIGH=High
# profile.HIGH.comment=High quality. Modern Nvidia or AMD graphics card required.

# Option screen configuration
# Main screen
screen=<list of options>
# Sub-screen
screen.NAME=<list of options>

# Screen options
#   OPTION    - option name
#   [NAME]    - link to sub-screen NAME
#   <profile> - profile selection
#   <empty>   - empty slot
#   *         - the rest of the options not configured on any of the screens

# Option columns per screen
# By default the options are shown in two columns:
#   1  2
#   3  4
#   5  6
#   ...
# When more than 18 options are present the screen switches to 3 or more columns.
# The option names are automatically shortened to avoid text overflow outside the button.
#
# Main screen
screen.columns=2
# Sub-screen
screen.NAME.columns=2

# Example:
# screen=<profile> <empty> BLOOM CELLSHADING [SUN_EFFECTS] [WAVING_GRASS]
# screen.SUN_EFFECTS=SUN_EFFECTS GOD_RAYS LENS_FLARE RAINDROPS
# screen.WAVING_GRASS=WAVING_GRASS WAVING_LEAVES WAVING_VINES
# screen.MISC=*
#
# User friendly screen labels can be loaded from language files in "/shaders/lang"
# Example from "/shaders/lang/en_US.lang"
# screen.DOF=Depth of field
# screen.DOF.comment=Depth of field effect. Adds blur to out of focus objects.
# screen.WAVING=Waving grass and leaves
# screen.WAVING.comment=Waving grass, leaves, fire and entities

# Custom uniforms
# Define custom variables and uniforms using general mathematical expressions with brackets, 
# constants, variables, operators and functions.
# The uniforms are sent to the shaders, the variables can be used in other variables or uniforms.
# The custom uniforms are updated on program change.
# 
# Constants
#   floating point number
#   pi - 3.1415926
#   true
#   false
#
# The available biome ids, categories and precipitation types are defines as constants. 
# For example: BIOME_PLAINS, BIOME_DESERT, BIOME_EXTREME_HILLS, etc. 
#
# Parameters (float)
#   biome                - biome id
#   temperature          - 0.0 to 1.0
#   rainfall             - 0.0 to 1.0 (humidity)
#
# Rain/snow is rendered for "biome_precipitation != PPT_NONE". If "temperature >= 0.15" rain is rendered, otherwise snow. 
#
# The fixed scalar uniforms are also available as parameters. For example: heldItemId, worldTime, moonPhase, etc.
# Vector elements can be accessed with suffix ".x", ".y" and ".z". For example: "sunPosition.y". 
# Color elements can be accessed with suffix ".r", ".g" and ".b". For example: "skyColor.r".
# Matrix elements can be accessed by row and column index. For example "gbufferModelView.0.1".
#
# The dynamic uniforms entityColor, entityId, blockEntityId, fogMode and fogColor can not be used as parameters 
# as they may change many times per program. 
#
# Parameters (boolean)
#   is_alive
#   is_burning
#   is_child
#   is_hurt
#   is_in_lava
#   is_in_water
#   is_invisible
#   is_on_ground
#   is_ridden
#   is_riding
#   is_sneaking
#   is_sprinting
#   is_wet
#
# Operators
#   +, -, *, /, %
#   !, &&, || 
#   >, >=, <, <=, ==, !=
#
# Functions
#   sin(x)
#   cos(x)
#   asin(x)
#   acos(x)
#   tan(x)
#   atan(x)
#   atan2(y, x)
#   torad(deg)
#   todeg(rad)
#   min(x, y ,...)
#   max(x, y, ...)
#   clamp(x, min, max)                             Limits a value to be between min and max values
#   abs(x)
#   floor(x)
#   ceil(x)
#   exp(x)
#   frac(x)
#   log(x)
#   pow(x)
#   random()
#   round(x)
#   signum(x)
#   sqrt(x)
#   fmod(x, y)                                     Similar to Math.floorMod()
#   if(cond, val, [cond2, val2, ...], val_else)    Select a value based one or more conditions
#   smooth([id], val, [fadeInTime, [fadeOutTime]]) Smooths a variable with custom fade-in time. 
#                                                  The "id" must be unique, if not specified it is generated automatically  
#                                                  Default fade time is 1 sec.
# Boolean functions                 
#   between(x, min, max)                           Check if a value is between min and max values
#   equals(x, y, epsilon)                          Compare two float values with error margin
#   in(x, val1, val2, ...)                         Check if a value equals one of several values
#
# Vector functions
#   vec2(x, y)
#   vec3(x, y, z) 
#   vec4(x, y, z, w)
#
# Example:
#   variable.bool.isBiomeDark=in(biome, BIOME_RIVER, BIOME_FOREST)
#   variable.float.valBiomeDark=smooth(1, if(isBiomeDark, 1, 0), 5)
#   variable.float.valHurtDark=smooth(2, if(is_hurt, 1.3, 0), 0, 10)
#   variable.float.valSwordDark=smooth(3, if(heldItemId == 276, 1, 0), 0.5, 0.5)
#   uniform.float.screenDark=max(valBiomeDark, valHurtDark, valSwordDark)
#   uniform.vec3.screenDark3=vec3(screenDark, heldItemId, biome)
#
uniform.<float|int|bool|vec2|vec3|vec4>.<name>=<expression>
variable.<float|int|bool|vec2|vec3|vec4>.<name>=<expression>

# Alpha test
# The alpha test can be configured per program.
# Where 
#  - func is one of: NEVER, LESS, EQUAL, LEQUAL, GREATER, NOTEQUAL, GEQUAL, GL_ALWAYS
#  - ref - float value
alphaTest.<program>=<off|func ref>

# Blend mode
# The blend mode can be configured per program.
# Where src, dst, srcA and dstA are one of: ZERO, ONE, SRC_COLOR, ONE_MINUS_SRC_COLOR, DST_COLOR, ONE_MINUS_DST_COLOR, 
# SRC_ALPHA, ONE_MINUS_SRC_ALPHA, DST_ALPHA, ONE_MINUS_DST_ALPHA, SRC_ALPHA_SATURATE
blend.<program>=<off|src dst srcA dstA>

# Composite render scale
# Defines a custom viewport to be used when rendering composite and deferred programs.
# The scale, offsetX and offsetY should be between 0.0 and 1.0.
scale.<program>=<scale|scale offsetX ofsetY>

# Ping-pong buffer flip
# Enable or disable ping-pong buffer flip for a specific buffer name in a specific composite or deferred program.
# When buffer flip is disabled the next composite program will use the same input and output buffers for this buffer name.
# The last program that writes to the buffer should have flip enabled so that the following programs can read from the buffer.
# This can be used with composite render scale to allow several composite programs to write to different regions in the same buffer.
# Forced buffer flip can be used to read from both ping-pong buffers.
flip.<program>.<buffer>=<true|false>

# Enable or disable programs depending on shader options
# Disabled progams are processed as not defined and instead their fallback programs will be used.
# The program name can contain dimension folder, for example:
#   program.world-1/composite2.enabled=BLOOM
# The expression is a boolean expression which can use shader options of type switch (on/off), for example:
#   program.composite.enabled=(BLOOM || SSAO) && !GODRAYS
program.<program>.enabled=<expression>

# Shaders can be separated by world dimension by placing them in folder "/shaders/world<id>" where "id" is the world dimension.
# When the world folder is present the shaders will be loaded only from there ignoring the default folder.
# Creating an empty world folder effectively disables the shaders for this world dimension.
# Mod world dimensions should also work.
# Only ".vsh" and ".fsh" files are loaded from the dimension folder.
# Example
#   /shaders         - default shaders
#   /shaders/world-1 - nether shaders
#   /shaders/world1  - end shaders
#
# Dimension folders are also scanned for options.
# The options in dimension foldes may be given different names to avoid conflict with default values.

# The "#include" directive found in ".vsh" and ".fsh" files is replaced with the contents of the included file
# Relative, look in same folder
#   #include "const.inc"
# Absolute, start from base folder "/shaders"
#   #include "/world-55/lib.inc"
# 
# Included files may include other files.
# The maximum include depth is limited to 10.
#
# To avoid code duplication on nested includes the following can be used:
#   // File A
#   #ifndef INCLUDE_A
#   #define INCLUDE_A
#   ... <code>
#   #endif
# 
# When Minecraft is started with argument "-Dshaders.debug.save=true" then the final shaders will be saved in "shaderpacks/debug".