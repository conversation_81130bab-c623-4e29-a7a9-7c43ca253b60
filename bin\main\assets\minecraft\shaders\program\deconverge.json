{"blend": {"func": "add", "srcrgb": "one", "dstrgb": "zero"}, "vertex": "sobel", "fragment": "deconverge", "attributes": ["Position"], "samplers": [{"name": "DiffuseSampler"}], "uniforms": [{"name": "ProjMat", "type": "matrix4x4", "count": 16, "values": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]}, {"name": "InSize", "type": "float", "count": 2, "values": [1.0, 1.0]}, {"name": "OutSize", "type": "float", "count": 2, "values": [1.0, 1.0]}, {"name": "ConvergeX", "type": "float", "count": 3, "values": [-4.0, 0.0, 2.0]}, {"name": "ConvergeY", "type": "float", "count": 3, "values": [0.0, -4.0, 2.0]}, {"name": "RadialConvergeX", "type": "float", "count": 3, "values": [1.0, 1.0, 1.0]}, {"name": "RadialConvergeY", "type": "float", "count": 3, "values": [1.0, 1.0, 1.0]}]}