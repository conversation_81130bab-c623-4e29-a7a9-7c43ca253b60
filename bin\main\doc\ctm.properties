###############################################################################
# Sample configuration for OptiFine's Connected Textures feature.
# Based on the configuration for <PERSON><PERSON><PERSON><PERSON>'s Connected Textures mod.
#
# Not implemented:
# - renderPass
#
###############################################################################
# ctm.properties
###############################################################################
# Sample ctm.properties file for use with <PERSON><PERSON><PERSON><PERSON>'s extended Connected
# Textures mod.
#
# This file is offered without any copyright restrictions. Please copy and
# modify it to suit your needs.
#
# For each block or terrain tile you wish to override with connected or random
# textures, use this template and create a .properties file in the
# mcpatcher/ctm folder of your texture pack.  Properties files can be organized
# into subfolders of any depth, as long as everything is within the top-level
# mcpatcher/ctm folder.
#
# Different types of connected texture methods are available with different
# requirements and restrictions.  See below for details for each method.
#
# All property names are case-sensitive.
# All paths are relative to assets/minecraft unless otherwise stated.
###############################################################################

###############################################################################
# General properties used by all methods:
###############################################################################

# (Optional) List of block and/or tiles this method should apply to.
# Multiple .properties file can refer to the same block/tile and they will be
# processed in alphabetical order by filename.  All tile-based entries are
# checked before block ID-based ones.  The first match wins.
matchTiles=<list of matching tile names>

# To refer to a tile from vanilla MC, simply use its name in textures/blocks:
#   matchTiles=dirt
# To refer to a tile from a mod, you will need to know its name
#   matchTiles=gearbox
# Tiles output by CTM rules can also be matched by another rule.  The tile name
# is simply the full path to the tile
#   matchTiles=mcpatcher/ctm/mygrass/1.png
matchBlocks=<list of block IDs 0-4095>

# These two properties can be omitted if they can be inferred from the filename
# instead:  mcpatcher/ctm/xxx/block<id><a-z suffix>.properties assumes
#   matchBlocks=<id>
#   matchTiles=
# unless you specify either property explicitly.

# (Optional) If multiple properties files match the same block, the highest
# weighted one is used.  In the event of a tie, the properties filenames are
# compared next.  The default weight is 0.
weight=<integer>

# (Required) Method to use when choosing a block's replacement texture:
# Methods:
#   ctm: Standard 8-way method (glass in the original CTM), uses 47 tiles.
#   ctm_compact: Compact 8-way method, uses 5 tiles.
#   horizontal: Connect to blocks on left and right only (bookshelves).
#   vertical: Connect to blocks above and below only.
#   horizontal+vertical: Connect horizontally, then vertically.
#   vertical+horizontal: Connect vertically, then horizontally.
#   top: Connect to block above only (sandstone).
#   random: Pick a tile at random.
#   repeat: Repeat a fixed pattern over large areas.
#   fixed: Use a single fixed tile.  Equivalent to random with only one tile.
#   overlay: Overlay for block transitions, uses 17 tiles.
#   overlay_ctm: Overlay variant of method "ctm".
#   overlay_random: Overlay variant of method "random".
#   overlay_repeat: Overlay variant of method "repeat".
#   overlay_fixed: Overlay variant of method "fixed".
# The overlay methods can be combined with other methods if they come 
# before them in the processing order (alphabetically). 
# The method "ctm_compact" is not compatible with any of the overlay methods.
method=<method>

# (Required) List of replacment tiles to use.  Each tile must be a separate
# image, just like terrain and item textures.  Tiles can be specified in
# several ways
#   0                   -> 0.png
#   8-11                -> 8.png, 9.png, 10.png, 11.png
#   name                -> name.png
#   name.png            -> name.png
#   <skip>              -> skip the tile, continue with next CTM properties
#   <default>           -> use the default texture for that block/tile
#   full/path/name.png  -> full/path/name.png
# In all cases but the last, the png file must be in the same directory as
# the properties file itself.
# The formats can be mixed and matched, e.g.
#   tiles=0-4 5 some/other/name.png
# The overlay methods may use the special name "<skip>" for empty tiles 
# that are to be skipped. The overlay methods can't use the tile name "<default>".
tiles=<list of tiles>

# (Optional) Connect type.  For methods that connect to adjacent blocks,
# specify how the game should decide if two blocks should be connected.
# block: Connect if block id of this block = block id of neighbor.
# tile: Connect if tile texture of this block = tile of neighbor.
# material: Connect if block material (stone, dirt, etc.) = neighbor's.
# state: Connect if block state (block + properties) = neighbour's
# The default is block for block-based properties files and tile for
# tile-based.
connect=<block | tile | material | state>

# (Optional) Connect tiles. Only for method "overlay"
# Connects only to blocks which are using the specified tiles 
connectTiles=<list of tiles>

# (Optional) Connect blocks. Only for method "overlay"
# Connects only to specified blocks 
connectBlocks=<list of blocks>

# (Optional) Faces.  Limit the mod to only certain faces of the block.
# bottom: Bottom face (negative y).
# top: Top face (positive y).
# north: North face (negative z).
# south: South face (positive z).
# east: East face (positive x).
# west: West face (negative x).
# sides: Shorthand for north south east west.
# all: All sides.
# NOTE:  This property is ignored on non-standard blocks.
faces=<combination of: north south east west top bottom sides all>

# (Optional) Metadata.  Limit the mod to only blocks with certain metadata
# values.  Can be any list of values between 0 and 31.
metadata=<list of values between 0 and 31>

# (Optional) Biome and height restrictions.  Limit the mod to only certain
# biomes or height ranges.  See mob.properties for a list of valid biome names.
# The legacy properties "minHeight" and "maxHeight" are also recognized.
biomes=<biome list>
heights=<height ranges>

# (Optional) Render pass used with the Better Glass mod.  Specifies the render
# pass during which the new texture should be used.  Otherwise it is used for
# the block's default rendering pass.
# See renderpass.properties for valid values here.
renderPass=<pass>
# NOTE: Due to renderer limitations, specifying a renderPass requires block
# ID-based ctm (either by naming the file block<nnn>.properties or using
# matchBlocks=... instead of matchTiles=...), not tile-based.
#
# To make colored glass, you need two sets of tiles: one for the frame and
# another for the overlay.  Here is an example, assuming tiles 0-46 are the
# frame and 47-93 are the overlay.
#
# mcpatcher/ctm/glass/glass1.properties:
#   matchBlocks=glass
#   method=ctm
#   tiles=0-46
#   renderPass=backface
#
# mcpatcher/ctm/glass/glass2.properties:
#   matchBlocks=glass
#   method=ctm
#   tiles=47-93
#   renderPass=overlay
#
# mcpatcher/ctm/glass/glass_pane1.properties:
#   matchBlocks=glass_pane
#   method=ctm
#   tiles=0-46
#
# mcpatcher/ctm/glass/glass_pane2.properties:
#   matchBlocks=glass_pane
#   method=ctm
#   tiles=47-93
#   renderPass=overlay

# (Optional) Compact CTM tile replacement. Only for method "ctm_compact"
# Allows to define replacement tile for a specific CTM case
# <ctm_index> is the index of the CTM case from the CTM template (0-46)
# <tile_index> is the index of the tile as defined in "tiles" (not the tile name!)
# With "ctm_compact" you can define more than 5 tiles and use the additional 
# tiles as replacements
ctm.<ctm_index>=<tile_index>  

# (Optional) Tint index. Only for method "overlay"
# Tint index for the tile texture
# Default is -1 (disabled
tintIndex=<index>

# (Optional) Tint block. Only for method "overlay"
# The block used for the tile texture tinting. 
# Different blocks use different colors for the same tint index. 
tintBlock=<block>

# (Optional) Layer. Only for method "overlay"
# The layer on which the overlay texture should be rendered
# Values:
#   cutout_mipped - transparent textures with mipmaps
#   cutout - transparent textures without mipmaps
#   translucent - translucent textures with mipmaps
# Default is "cutout_mipped" 
layer=<cutout_mipped|cutout|translucent>

# (Optional) Name
# Only for blocks which have corresponding nameable tile entities.
# For example:
#   beacon brewing_stand enchanting_table furnace dispenser dropper hopper
# See "custom_guis.properties" for the name matching syntax. 
name=<name>

###############################################################################
# Everything below here is for specific ctm methods.  Each .properties file
# should contain only one of these sections.
###############################################################################

###############################################################################
# Standard 8-way connected textures
###############################################################################

method=ctm
# (Required) List of 47 tiles to use.
tiles=<47 tiles>
# (Optional) Whether to show seams on inner edges when connecting to adjacent
# blocks.
innerSeams=false

###############################################################################
# Compact 8-way connected textures
###############################################################################

method=ctm_compact
# (Required) List of 5 tiles to use.
tiles=<5 tiles>
# (Optional) Whether to show seams on inner edges when connecting to adjacent
# blocks.
innerSeams=false

###############################################################################
# Compact 8-way connected textures with special cases
###############################################################################

method=ctm_compact
# (Required) List of 5 tiles to use.
tiles=<5 tiles> <additional tiles>
# (Optional) Indexes of replacement tiles for some CTM cases
ctm.46=5
ctm.17=6

###############################################################################
# Horizontal-only connected textures
###############################################################################

method=horizontal
# (Required) List of 4 tiles to use.
tiles=<4 tiles>

###############################################################################
# Vertical-only connected textures
###############################################################################

method=vertical
# (Required) List of 4 tiles to use.
tiles=<list of tiles>

###############################################################################
# Top connected textures
###############################################################################

method=top
# (Required) Only one tile is needed.
tiles=<single tile>

###############################################################################
# Random textures
###############################################################################

method=random
# (Required) List of any number of tiles to choose from.
tiles=<list of tiles>
# (Optional) List of weights to apply to the random choice.  For example, if
# you have
# tiles=6-11
# weights=10 1 10 2 7 3
# then tiles 6 and 8 will have a weight of 10, 7 will have a weight of 1, etc.
# Weights do not have to total 100 or any other particular value.  In the above
# example, tiles 6 and 8 will each be used ~30.3% (10/(10+1+10+2+7+3)) of the
# time and so forth.
weights=<same-size list of weights>
# Random loops
# Repeats the random function several times to increase randomness.
# Can be used to make different textures use different random patterns.
# Higher values may decrease the chunk loading speed.
# Default is 0.
randomLoops=<0-9>
# (Optional) Desired level of symmetry for the faces of each block.  Applies to
# standard 6-sided blocks only.
# none: All 6 faces are textured independently.  This is the default.
# opposite: 2-way symmetry; opposing faces have the same texture, but each pair
# can potentially have a different texture.
# all: All 6 faces have the same texture.
symmetry=<none | opposite | all>
# (Optional) Texture linking between related blocks.
# If true, MCPatcher uses the same random number seed for all parts of a
# multiblock object, for example, the top and bottom halves of tall grass.
# This allows you to create randomized textures that will remain consistent
# within each set of blocks.  If this property is false, the two halves will
# be "scrambled", i.e., chosen independently.
#
# NOTE: For this to work properly, you'll need multiple properties files with
# linked=true and the same number of replacement textures and same set of
# weights.  For example,
# double_plant_top.properties:
#   method=random
#   tiles=grass_top1 grass_top2 grass_top3
#   weights=1 2 3
#   linked=true

# double_plant_bottom.properties:
#   method=random
#   tiles=grass_bottom1 grass_bottom2 grass_bottom3
#   weights=1 2 3
#   linked=true
#
# The default is false.  The linked property currently applies only to plants
# (e.g., reeds), double plants, and doors.
linked=<true | false>

###############################################################################
# Repeat pattern textures
###############################################################################

method=repeat
# (Required) Width of the repeating pattern.
width=<width of pattern>
# (Required) Height of the repeating pattern.
height=<height of pattern>
# (Required) List of exactly width * height tiles.
tiles=<list of width * height tiles>
# (Optional) Desired level of symmetry for the faces of each block.  Applies to
# standard 6-sided blocks only.
# none: All 6 faces are textured so that the pattern tiling looks the same from
# all sides.  This is the default.
# opposite: 2-way symmetry; opposing faces have the same texture, which means
# that tiling on the south and east faces will be mirrored left-to-right from
# compared to the north and west faces.
symmetry=<none | opposite>

###############################################################################
# Fixed textures
###############################################################################

method=fixed
# (Required) Single tile to use.
tiles=<single tile>
