###############################################################################
# Sample configuration for OptiFine's Custom Sky feature.
# Based on the configuration for MCPatcher's Better Skies mod.
#
###############################################################################
# sky.properties
###############################################################################
# Sample sky.properties file for use with MCPatcher's Better Skies mod.
#
# This file is offered without any copyright restrictions. Please copy and
# modify it to suit your needs.
#
# All property names are case-sensitive.
# Properties that specify a path to a texture file can use any of the following
# syntax:
# Relative to assets/minecraft:
#   path/filename -> assets/minecraft/path/filename
# Relative to assets/minecraft/mcpatcher:
#   ~/path/filename -> assets/minecraft/mcpatcher/filename
# Relative to location of properties file:
#   ./path/filename -> (path of properties file)/path/filename
# Absolute path with namespace:
#   namespace:path/filename -> assets/namespace/path/filename
#
# Place the file at
#   ~/sky/world0/sky0.properties
#   ~/sky/world0/sky1.properties
#   ... etc.
# in your texture pack.  Each file represents one layer of the sky.  MCPatcher
# will continue loading them until a .properties file is not found.  The order
# is the order in which they will be rendered in game.
#
# Additionally, two special properties files are applied to the sun and moon if
# present.  This is mainly intended to allow you to override the blend method
# used by the sun and moon.
#   ~/sky/world0/sun.properties  (replaces sun.png)
#   ~/sky/world0/moon_phases.properties (replaces moon_phases.png)
# Instead of a full skybox, the source texture should match the layout of
# sun.png or moon_phases.png.
#
# NOTE: The "world0" in the path refers to the overworld.  If there were other
# worlds with skies (the Nether and End do not use the standard sky rendering
# methods), their files would be in ~/sky/world<world number>.
###############################################################################

###############################################################################
# Sky properties
###############################################################################

# (Optional) Name of source texture.  This can be anywhere in your texture pack
# and multiple properties files can share the same source.  If not specified,
# sky<n>.png in the same directory is used.
source=<texture>

# (Required) Fade in/out times.  All times are in hh:mm 24-hour format.  For
# reference,
#   Sunrise  =  6:00 = /time set 0
#   Noon     = 12:00 = /time set 6000
#   Sunset   = 18:00 = /time set 12000
#   Midnight =  0:00 = /time set 18000
# The fade times control the brightness when blending.
#   between startFadeIn and endFadeIn:   0 up to 1
#   between endFadeIn and startFadeOut:  always 1
#   between startFadeOut and endFadeOut: 1 down to 0
#   between endFadeOut and startFadeIn:  always 0
# Note that you do not need to specify startFadeOut; its value is uniquely
# determined by the other three.
startFadeIn=<hh:mm>
endFadeIn=<hh:mm>
endFadeOut=<hh:mm>

# (Optional) Blending method.  Here "previous layer" can refer to the default
# sky or to the previous custom sky defined by sky<n-1>.properties.  Supported
# blending methods are
#   add:      Add this sky bitmap to the previous layer.
#   subtract:
#   multiply: Multiply the previous RGBA values by the RGBA values in the
#             current bitmap.
#   dodge:
#   burn:
#   screen:
#   replace:  Replace the previous layer entirely with the current bitmap.
#             There is no gradual fading with this method; if brightness
#             computed from the fade times is > 0, the full pixel value is
#             used.
#   overlay:  RGB value > 0.5 brightens the image, < 0.5 darkens.
#   alpha:    Weighted average by alpha value.
# The default method is add.
blend=add

# (Optional) Rotation.  Whether or not the bitmap should rotate with the time
# of day.  The default is true.  The speed and direction of rotation can also
# be controlled.
rotate=true

# (Optional) Rotation speed as a multiple of the default of one 360-degree
# cycle per game day.  A value of 0.5 rotates every two days.  Irrational
# values can be useful to make clouds appear in different positions each day,
# for example.
# NOTE:  This does not affect the fading in and out which always occurs on a
# 24-hour cycle.
speed=1.0

# (Optional) Axis of rotation.  If a player is looking in the given direction,
# the skybox will appear to be rotating clockwise around the line of sight.
# Default rotation is along the southern axis (rising in the east and setting
# in the west).
# For reference, the vectors corresponding to the six cardinal directions are
# below.  However, the rotation axis can be any vector except 0 0 0.
# Normalization is not required.
#   down  =  0 -1  0
#   up    =  0  1  0
#   north =  0  0 -1
#   south =  0  0  1
#   west  = -1  0  0
#   east  =  1  0  0
axis=0.0 0.0 1.0

# (Optional) Weather
# Weather for which the layer is to be rendered
# Several values can be specified separated by space 
# for example "weather=clear rain thunder"
# Default is "clear"
weather=clear|rain|thunder

# (Optional) Biome and height
# Limit the sky layer to only certain biomes or height ranges.
# See mob.properties for a list of valid biome names.
biomes=<biome list>
heights=<height ranges>

# (Optional) Transition
# Transition time (sec) for the layer brightness.
# It is used to smooth sharp transitions, for example between different biomes.
# Default is 1 sec. 
transition=1
