# Translator = sp614x, Diego R. V.

# Video Settings

options.anaglyph.tooltip.2=Enables a stereoscopic 3D effect using different colours

of.options.shaders.OLD_HAND_LIGHT.tooltip.6=recognise light emitting items in the main hand 

of.options.shaders.SHADOW_RES_MUL.tooltip.7=Lower values = inexact, coarser shadows.

# Quality

of.options.AA_LEVEL.tooltip.5=sharp colour transitions.

of.options.CUSTOM_COLORS=Custom Colours
of.options.CUSTOM_COLORS.tooltip.1=Custom Colours
of.options.CUSTOM_COLORS.tooltip.2=  ON - uses custom colours (default), slower
of.options.CUSTOM_COLORS.tooltip.3=  OFF - uses default colours, faster
of.options.CUSTOM_COLORS.tooltip.4=The custom colours are supplied by the current

of.options.SWAMP_COLORS=Swamp Colours
of.options.SWAMP_COLORS.tooltip.1=Swamp Colours
of.options.SWAMP_COLORS.tooltip.2=  ON - use swamp colours (default), slower
of.options.SWAMP_COLORS.tooltip.3=  OFF - do not use swamp colours, faster
of.options.SWAMP_COLORS.tooltip.4=The swamp colours affect grass, leaves, vines and water.

of.options.SMOOTH_BIOMES.tooltip.5=averaging the colour of all surrounding blocks.

# Details

of.options.TRANSLUCENT_BLOCKS.tooltip.3=  Fancy - correct colour blending (slower)
of.options.TRANSLUCENT_BLOCKS.tooltip.4=  Fast - fast colour blending (faster)
of.options.TRANSLUCENT_BLOCKS.tooltip.5=Controls the colour blending of translucent blocks
of.options.TRANSLUCENT_BLOCKS.tooltip.6=with different colour (stained glass, water, ice)

options.biomeBlendRadius.tooltip.1=Smooths the colour transition between biomes

# Performance

of.options.SMOOTH_FPS.tooltip.1=Stabilises FPS by flushing the graphic driver buffers.
of.options.SMOOTH_FPS.tooltip.2=  OFF - no stabilisation, FPS may fluctuate
of.options.SMOOTH_FPS.tooltip.3=  ON - FPS stabilisation

of.options.SMOOTH_WORLD.tooltip.2=  OFF - no stabilisation, FPS may fluctuate
of.options.SMOOTH_WORLD.tooltip.3=  ON - FPS stabilisation
of.options.SMOOTH_WORLD.tooltip.4=Stabilises FPS by distributing the internal server load.

of.options.FAST_RENDER.tooltip.3= ON - optimised rendering (faster)
of.options.FAST_RENDER.tooltip.4=Uses optimised rendering algorithm which decreases

of.options.FAST_MATH=Fast Maths
of.options.FAST_MATH.tooltip.1=Fast Maths
of.options.FAST_MATH.tooltip.2= OFF - standard maths (default)
of.options.FAST_MATH.tooltip.3= ON - faster maths
of.options.FAST_MATH.tooltip.4=Uses optimised sin() and cos() functions which can
of.options.FAST_MATH.tooltip.5=better utilise the CPU cache and increase the FPS.

of.options.CHUNK_UPDATES.tooltip.6=higher values may destabilise the framerate.

of.options.RENDER_REGIONS.tooltip.4=Allows faster terrain rendering by optimising
