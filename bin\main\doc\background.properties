###############################################################################
# Sample configuration for OptiFine's Custom Panorama feature.
###############################################################################
# background.properties
###############################################################################
# This file is offered without any copyright restrictions. Please copy and
# modify it to suit your needs.
#
# Location: "/assets/minecraft/optifine/gui/background.properties"
# Controls the behaviour of the main menu panorama
# 
# Alternative panorama folders:
#   /assets/minecraft/optifine/gui/background1 
#   /assets/minecraft/optifine/gui/background2 
#   /assets/minecraft/optifine/gui/background3 
#   ...
#
# The alternative panorama folders should contain the following textures
#   panorama_0.png 
#   panorama_1.png 
#   panorama_2.png 
#   panorama_3.png 
#   panorama_4.png 
#   panorama_5.png
# 
# The alternative panorama folders can include "background.properties" to
# define custom properties for the panorama

# Weight (optional)
# Weight for the random selection, higher weights will be selected more often
# Default is 1
weight=<weight> 

# Blur level (optional)
# The main menu background uses 3 types of blur 
# Higher blur levels may decrease the main menu FPS 
blur1=<1-64>
blur2=<1-3>
blur3=<1-3>

# Overlay colors (optional)
# There are 2 gradient overlays drawn over the background image
# When the top and bottom colors are 0 the overlay is disabled 
# The color format is ARGB (hex)
# Default values are shown below
overlay1.top=80FFFFFF
overlay1.bottom=00FFFFFF
overlay2.top=00000000
overlay2.bottom=80000000
