###############################################################################
# Sample configuration for OptiFine's Custom Items feature.
# Based on the configuration for MCPatcher's Custom Item Textures mod.
#
# Not implemented
# - method
# - cap
# - fade
#
###############################################################################
# cit.properties
###############################################################################
# Sample cit.properties file for use with MCPatcher's Custom Item Textures mod.
#
# This file is offered without any copyright restrictions. Please copy and
# modify it to suit your needs.
#
# This file contains global properties for the Custom Item Textures mod and
# should be in the mcpatcher/cit folder of the texture pack.  For individual
# item textures, see cit_single.properties.
#
# All property names are case-sensitive.
# All paths are relative to assets/minecraft unless otherwise stated.
###############################################################################

# (Optional) Specify how to apply multiple effects to the same item.
# Depending on the method chosen, multiple effects can be rendered with
# different intensities from 0 (not visible) to 1 (fully visible).
# average: Weighted average by enchantment level.
#              intensity = enchantment_level / sum(enchantment_levels)
# layered: Similar to average, but max is used instead of sum.
#              intensity = enchantment_level / max(enchantment_levels)
# cycle:   Cycle through each effect in turn.  The duration of each effect
#          can be set via the duration property.  The [group] value if present
#          allows multiple sets of effects to be cycled independently.
# Note that average and layered with cap=1 are equivalent and will both show
# only the "dominant" enchantment on an item.
method=<average | layered | cycle>

# (Optional) Specify how many layers can render for average/layered method.
# The topmost layers have priority over bottommost layers as determined by
# the layer value of each effect.
cap=<count>

# (Optional) The speed at which one effect transitions to another in a cycle.
# This does not affect the duration of the actual effect when displayed -- for
# that use the effect's duration property.
# The default is 0.5 seconds.
fade=<seconds>

# (Optional) Whether to use the default glint.png enchantment.  If true,
# glint.png is used if no other custom enchantment effect matches.  If set to
# false, the default glint.png enchantment stops rendering completely.  This is
# important for items that have no specific enchantment, but have an
# enchantment effect -- such as potions and golden apples.
# The default is true.
useGlint=<true | false>
