###############################################################################
# Sample configuration for OptiFine's Custom Colors feature.
# Based on the configuration for MCPatcher's Custom Colors mod.
#
###############################################################################
# color.properties
###############################################################################
# Sample color.properties file for use with MCPatcher's Custom Colors mod.
#
# This file is offered without any copyright restrictions. Please copy and modify 
# it to suit your needs.  Then place it in the folder "assets/minecraft/mcpatcher" 
# of your resource pack.
#
# You only need to provide values for the properties you wish to change.  The
# default Minecraft values for each property are given below for convenience.
#
# All property names are case-sensitive.
# All colors are in hex rgb format, 000000 to ffffff.
# All paths are relative to assets/minecraft unless otherwise stated.
###############################################################################

###############################################################################
# Base color of particle effects
###############################################################################
# Base water particle (splashes, bubbles, drops) color.  Biome water color
# multiplier is applied to this value.  The value should match the color of
# your base water texture.  If your base water texture is grey so that you do
# coloring via misc/watercolorX.png, you should set this to ffffff.
particle.water=334cff
# Base portal particle color.  A random multiplier between 0.4 and 1.0 is
# applied to all three r/g/b values.
particle.portal=ff4ce5

###############################################################################
# Nether and End colors
###############################################################################
fog.nether=330707
fog.end=181318
sky.end=282828

###############################################################################
# Lily pad color
###############################################################################
# This is a single color that is used across all biomes.
lilypad=208030

###############################################################################
# Colors for each potion effect
###############################################################################
# Final color is the average of these colors weighted by level of each potion
# effect.
potion.absorption=2552a5
potion.blindness=1f1f23
potion.confusion=551d4a
potion.damageBoost=932423
potion.digSlowDown=4a4217
potion.digSpeed=d9c043
potion.fireResistance=e49a3a
potion.harm=430a09
potion.heal=f82423
potion.healthBoost=f87d23
potion.hunger=587653
potion.invisibility=7f8392
potion.jump=786297
potion.moveSlowdown=5a6c81
potion.moveSpeed=7cafc6
potion.nightVision=1f1fa1
potion.poison=4e9331
potion.regeneration=cd5cab
potion.resistance=99453a
potion.saturation=f82423
potion.waterBreathing=2e5299
potion.weakness=484d48
potion.wither=352a27
# potion.water represents a plain bottle of water
potion.water=385dc6

###############################################################################
# Spawner egg colors
###############################################################################
egg.shell.Creeper=0da70b
egg.spots.Creeper=000000
egg.shell.Skeleton=c1c1c1
egg.spots.Skeleton=494949
egg.shell.Spider=342d27
egg.spots.Spider=a80e0e
egg.shell.Zombie=00afaf
egg.spots.Zombie=799c65
egg.shell.Slime=51a03e
egg.spots.Slime=7ebf6e
egg.shell.Ghast=f9f9f9
egg.spots.Ghast=bcbcbc
egg.shell.PigZombie=ea9393
egg.spots.PigZombie=4c7129
egg.shell.Enderman=161616
egg.spots.Enderman=000000
egg.shell.CaveSpider=0c424e
egg.spots.CaveSpider=a80e0e
egg.shell.Silverfish=6e6e6e
egg.spots.Silverfish=303030
egg.shell.Blaze=f6b201
egg.spots.Blaze=fff87e
egg.shell.LavaSlime=340000
egg.spots.LavaSlime=fcfc00
egg.shell.Bat=4c3e30
egg.spots.Bat=0f0f0f
egg.shell.Witch=340000
egg.spots.Witch=51a03e
egg.shell.Endermite=161616
egg.spots.Endermite=6e6e6e
egg.shell.Pig=f0a5a2
egg.spots.Pig=db635f
egg.shell.Sheep=e7e7e7
egg.spots.Sheep=ffb5b5
egg.shell.Cow=443626
egg.spots.Cow=a1a1a1
egg.shell.Chicken=a1a1a1
egg.spots.Chicken=ff0000
egg.shell.Squid=223b4d
egg.spots.Squid=708899
egg.shell.Wolf=d7d3d3
egg.spots.Wolf=ceaf96
egg.shell.MushroomCow=a00f10
egg.spots.MushroomCow=b7b7b7
egg.shell.Ozelot=efde7d
egg.spots.Ozelot=564434
egg.shell.EntityHorse=c09e7d
egg.spots.EntityHorse=eee500
egg.shell.Rabbit=995f40
egg.spots.Rabbit=734831
egg.shell.Villager=563c33
egg.spots.Villager=bd8b72

###############################################################################
# Map colors
# Aliases
#   map.snow=map.white
#   map.adobe=map.orange
#   map.lightBlue=map.light_blue
###############################################################################
# Block map colors
map.air=000000
map.grass=7fb238
map.sand=f7e9a3
map.cloth=c7c7c7
map.tnt=ff0000
map.ice=a0a0ff
map.iron=a7a7a7
map.foliage=007c00
map.clay=a4a8b8
map.dirt=976d4d
map.stone=707070
map.water=4040ff
map.wood=8f7748
map.quartz=fffcf5
map.gold=faee4d
map.diamond=5cdbd5
map.lapis=4a80ff
map.emerald=00d93a
map.podzol=815631
map.netherrack=700200
# General map colors
map.white=ffffff
map.orange=d87f33
map.magenta=b24cd8
map.light_blue=6699d8
map.yellow=e5e533
map.lime=7fcc19
map.pink=f27fa5
map.gray=4c4c4c
map.silver=999999
map.cyan=4c7f99
map.purple=7f3fb2
map.blue=334cb2
map.brown=664c33
map.green=667f33
map.red=993333
map.black=191919

# Banners use the general map colors.
# The banner colors can not be configured separately from the map colors.

###############################################################################
# Sheep colors
###############################################################################
sheep.white=ffffff
sheep.orange=f2b233
sheep.magenta=e57fd8
sheep.lightBlue=99b2f2
sheep.yellow=e5e533
sheep.lime=7fcc19
sheep.pink=f2b2cc
sheep.gray=4c4c4c
sheep.silver=999999
sheep.cyan=4c99b2
sheep.purple=b266e5
sheep.blue=3366cc
sheep.brown=7f664c
sheep.green=667f33
sheep.red=cc4c4c
sheep.black=191919

###############################################################################
# Armor colors
###############################################################################
armor.default=a06540
armor.white=ffffff
armor.orange=f2b233
armor.magenta=e57fd8
armor.lightBlue=99b2f2
armor.yellow=e5e533
armor.lime=7fcc19
armor.pink=f2b2cc
armor.gray=4c4c4c
armor.silver=999999
armor.cyan=4c99b2
armor.purple=b266e5
armor.blue=3366cc
armor.brown=7f664c
armor.green=667f33
armor.red=cc4c4c
armor.black=191919

###############################################################################
# Wolf collar colors
###############################################################################
collar.white=ffffff
collar.orange=f2b233
collar.magenta=e57fd8
collar.lightBlue=99b2f2
collar.yellow=e5e533
collar.lime=7fcc19
collar.pink=f2b2cc
collar.gray=4c4c4c
collar.silver=999999
collar.cyan=4c99b2
collar.purple=b266e5
collar.blue=3366cc
collar.brown=7f664c
collar.green=667f33
collar.red=cc4c4c
collar.black=191919

###############################################################################
# Dye colors
# NOTE: These values are in the game but are not actually used anywhere.
###############################################################################
dye.black=1e1b1b
dye.red=b3312c
dye.green=3b511a
dye.brown=51301a
dye.blue=253192
dye.purple=7b2fbe
dye.cyan=287697
dye.silver=287697
dye.gray=434343
dye.pink=d88198
dye.lime=41cd34
dye.yellow=decf2a
dye.lightBlue=6689d3
dye.magenta=c354cd
dye.orange=eb8844
dye.white=f0f0f0

###############################################################################
# Text colors
###############################################################################
# color of exp level text
text.xpbar=80ff20
# color of "Boss Health" text
text.boss=ff00ff
# color of sign text
text.sign=000000
# color codes generated by \247 + 0123456789abcdef
text.code.0=000000
text.code.1=0000aa
text.code.2=00aa00
text.code.3=00aaaa
text.code.4=aa0000
text.code.5=aa00aa
text.code.6=ffaa00
text.code.7=aaaaaa
text.code.8=555555
text.code.9=5555ff
text.code.10=55ff55
text.code.11=55ffff
text.code.12=ff5555
text.code.13=ff55ff
text.code.14=ffff55
text.code.15=ffffff
text.code.16=000000
text.code.17=00002a
text.code.18=002a00
text.code.19=002a2a
text.code.20=2a0000
text.code.21=2a002a
text.code.22=2a2a00
text.code.23=2a2a2a
text.code.24=151515
text.code.25=15153f
text.code.26=153f15
text.code.27=153f3f
text.code.28=3f1515
text.code.29=3f153f
text.code.30=3f3f15
text.code.31=3f3f3f

###############################################################################
# Other options
###############################################################################
# Override cloud type.  Note that the player can disable this override in the
# MCPatcher Options panel.
clouds=fast|fancy|none

# XpOrb animation duration (milliseconds)
# Default is 628 ms
xporb.time=628

###############################################################################
# Complete file list
###############################################################################
# Below is a full list of files used by the Custom Colors mod:
# NOTE: ~ is shorthand for the mcpatcher folder (assets/minecraft/mcpatcher)
# ~/color.properties - this file
# ~/colormap/redstone.png - 16x1 redstone colors (0=fully off, 15=fully on)
# ~/colormap/pumpkinstem.png - 8x1 pumpkin stem colors (0=sprout, 7=fully grown)
# ~/colormap/melonstem.png - 8x1 melon stem colors (0=sprout, 7=fully grown)
# ~/colormap/lavadrop.png - Nx1 lava drop colors (x=age of particle in ticks)
# ~/colormap/myceliumparticle.png - any size, random mycelium particle colors
# ~/colormap/xporb.png - any size, array of xp orb colors
# ~/colormap/durability.png - any size, array of item durability colors
#
# ~/colormap/swampgrass.png - 256x256 swamp grass color palette
# ~/colormap/swampfoliage.png - 256x256 swamp foliage color palette
# ~/colormap/pine.png - 256x256 pine tree color palette
# ~/colormap/birch.png - 256x256 birch tree color palette
# ~/colormap/water.png - 256x256 water color palette
# ~/colormap/underwater.png - 256x256 underwater color
# ~/colormap/underlava.png - 256x256 underlava color
# ~/colormap/fog0.png - 256x256 fog color for the overworld
# ~/colormap/sky0.png - 256x256 sky color for the overworld
#
# ~/lightmap/world0.png - Nx32 or Nx64 overworld lighting palettes
# ~/lightmap/world-1.png - Nx32 or Nx64 Nether lighting palettes
# ~/lightmap/world1.png - Nx32 or Nx64 End lighting palettes

###############################################################################
# Custom biome palettes
###############################################################################
# You may assign custom biome palettes to any standard block (i.e. one that
# does not already have its own special color multiplier method).  Each custom
# colormap should have a properties file in ~/colormap/custom.  See
# colormap.properties for more details.
#
# Default custom colormap format:
# Mojang-style temperature+humidity maps:
#   palette.format=vanilla
# MCPatcher-style grid (x=biome ID, y=height) maps:
#   palette.format=grid
# Note that this setting does not affect the vanilla foliage and grass
# colormaps in assets/minecraft/textures/colormap.  It can also be overridden
# per-colormap in each individual properties file in ~/colormap/custom
