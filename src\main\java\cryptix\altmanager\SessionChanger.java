package cryptix.altmanager;

import cryptix.altmanager.auth.MicrosoftAuthResult;
import cryptix.altmanager.auth.MicrosoftAuthenticationException;
import cryptix.altmanager.auth.MicrosoftAuthenticator;
import net.minecraft.client.Minecraft;
import net.minecraft.util.Session;

public class SessionChanger {
	private MicrosoftAuthenticator auth;
	private static SessionChanger instance;
	private final Minecraft mc = Minecraft.getMinecraft();
	public long timeSinceFail;
	public void loginCracked(String n) {
		mc.setSession(new Session(n, n, "0", "legacy"));
    }
	public void loginMicrosoft(String email, String password) {
		new Thread(() -> {
	        MicrosoftAuthenticator authenticator = new MicrosoftAuthenticator();
	        AltManagerGui.status = "§6Logging in";
	        try {
	            MicrosoftAuthResult acc = authenticator.loginWithCredentials(email, password);
	            if (acc != null) {
	                Minecraft.getMinecraft().setSession(
	                    new Session(
	                        acc.getProfile().getName(),
	                        acc.getProfile().getId(),
	                        acc.getAccessToken(),
	                        "legacy"
	                    )
	                );
	                System.out.println("Login successful");
	            } else {
	                System.out.println("Failed login");
	                timeSinceFail = System.currentTimeMillis();
	            }
	            AltManagerGui.status = "§aIdle";
	        } catch (MicrosoftAuthenticationException e) {
	            e.printStackTrace();
	            System.out.println("Failed login");
                timeSinceFail = System.currentTimeMillis();
	        }
	    }).start();
    }
	
	public String getUser(String email, String password) {
		MicrosoftAuthenticator authenticator = new MicrosoftAuthenticator();
		try {
			MicrosoftAuthResult acc = authenticator.loginWithCredentials(email, password);
			return acc.getProfile().getName();
		} catch (MicrosoftAuthenticationException e) {

		}
		return "";
	}
	
	public static SessionChanger instance() {
		if (instance == null) {
			instance = new SessionChanger();
		}

		return instance;
	}
	
}
