***************
*** 72,86 ****
     public <T extends TileEntity> TileEntitySpecialRenderer<T> func_147546_a(Class<? extends TileEntity> p_147546_1_) {
        TileEntitySpecialRenderer<? extends TileEntity> tileentityspecialrenderer = (TileEntitySpecialRenderer)this.field_147559_m.get(p_147546_1_);
        if(tileentityspecialrenderer == null && p_147546_1_ != TileEntity.class) {
-          tileentityspecialrenderer = this.<TileEntity>func_147546_a(p_147546_1_.getSuperclass());
           this.field_147559_m.put(p_147546_1_, tileentityspecialrenderer);
        }
  
-       return tileentityspecialrenderer;
     }
  
     public <T extends TileEntity> TileEntitySpecialRenderer<T> func_147547_b(TileEntity p_147547_1_) {
-       return p_147547_1_ == null?null:this.func_147546_a(p_147547_1_.getClass());
     }
  
     public void func_178470_a(World p_178470_1_, TextureManager p_178470_2_, FontRenderer p_178470_3_, Entity p_178470_4_, float p_178470_5_) {
--- 72,86 ----
     public <T extends TileEntity> TileEntitySpecialRenderer<T> func_147546_a(Class<? extends TileEntity> p_147546_1_) {
        TileEntitySpecialRenderer<? extends TileEntity> tileentityspecialrenderer = (TileEntitySpecialRenderer)this.field_147559_m.get(p_147546_1_);
        if(tileentityspecialrenderer == null && p_147546_1_ != TileEntity.class) {
+          tileentityspecialrenderer = this.<TileEntity>func_147546_a((Class<? extends TileEntity>)p_147546_1_.getSuperclass());
           this.field_147559_m.put(p_147546_1_, tileentityspecialrenderer);
        }
  
+       return (TileEntitySpecialRenderer<T>)tileentityspecialrenderer;
     }
  
     public <T extends TileEntity> TileEntitySpecialRenderer<T> func_147547_b(TileEntity p_147547_1_) {
+       return (TileEntitySpecialRenderer<T>)(p_147547_1_ == null?null:this.func_147546_a(p_147547_1_.getClass()));
     }
  
     public void func_178470_a(World p_178470_1_, TextureManager p_178470_2_, FontRenderer p_178470_3_, Entity p_178470_4_, float p_178470_5_) {
