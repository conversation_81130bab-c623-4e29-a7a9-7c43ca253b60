plugins {
    id 'java'
    id 'application'
}

group = 'client.cryptix'
version = '1.0'

sourceCompatibility = JavaVersion.VERSION_1_8
targetCompatibility = JavaVersion.VERSION_1_8

mainClassName = 'net.minecraft.client.main.Main' // or your own start class

repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    implementation files('libs/1.8.9/1.8.9.jar')
    implementation files('libs/org/lwjgl/lwjgl/lwjgl/2.9.4-nightly-20150209/lwjgl-2.9.4-nightly-20150209.jar')
    implementation files('libs/org/lwjgl/lwjgl/lwjgl_util/2.9.4-nightly-20150209/lwjgl_util-2.9.4-nightly-20150209.jar')
    implementation files('libs/net/java/jinput/jinput/2.0.5/jinput-2.0.5.jar')
    implementation files('libs/com/google/code/gson/gson/2.2.4/gson-2.2.4.jar')
    implementation files('libs/com/google/guava/guava/17.0/guava-17.0.jar')
    implementation files('libs/commons-codec/commons-codec/1.9/commons-codec-1.9.jar')
    implementation files('libs/commons-io/commons-io/2.4/commons-io-2.4.jar')
    implementation files('libs/commons-logging/commons-logging/1.1.3/commons-logging-1.1.3.jar')
    implementation files('libs/net/java/dev/jna/jna/3.4.0/jna-3.4.0.jar')
    implementation files('libs/net/java/dev/jna/platform/3.4.0/platform-3.4.0.jar')
    implementation files('libs/io/netty/netty-all/4.0.23.Final/netty-all-4.0.23.Final.jar')
    implementation files('libs/org/apache/logging/log4j/log4j-api/2.0-beta9/log4j-api-2.0-beta9.jar')
    implementation files('libs/org/apache/logging/log4j/log4j-core/2.0-beta9/log4j-core-2.0-beta9.jar')
    implementation files('libs/org/apache/commons/commons-compress/1.8.1/commons-compress-1.8.1.jar')
    implementation files('libs/org/apache/commons/commons-lang3/3.3.2/commons-lang3-3.3.2.jar')
    implementation files('libs/org/apache/httpcomponents/httpclient/4.3.3/httpclient-4.3.3.jar')
    implementation files('libs/org/apache/httpcomponents/httpcore/4.3.2/httpcore-4.3.2.jar')
    implementation files('libs/com/mojang/authlib/1.5.21/authlib-1.5.21.jar')
    implementation files('libs/com/mojang/realms/1.7.59/realms-1.7.59.jar')
    implementation files('libs/com/paulscode/codecjorbis/20101023/codecjorbis-20101023.jar')
    implementation files('libs/com/paulscode/codecwav/20101023/codecwav-20101023.jar')
    implementation files('libs/com/paulscode/libraryjavasound/20101123/libraryjavasound-20101123.jar')
    implementation files('libs/com/paulscode/librarylwjglopenal/20100824/librarylwjglopenal-20100824.jar')
    implementation files('libs/com/paulscode/soundsystem/20120107/soundsystem-20120107.jar')
    implementation files('libs/tv/twitch/twitch/6.5/twitch-6.5.jar')
    implementation files('libs/tv/twitch/twitch-platform/6.5/twitch-platform-6.5-natives-windows-64.jar')
    implementation files('libs/tv/twitch/twitch-external-platform/4.5/twitch-external-platform-4.5-natives-windows-64.jar')
    implementation files('libs/oshi-project/oshi-core/1.1/oshi-core-1.1.jar')
}

application {
    applicationDefaultJvmArgs = [
        "-Djava.library.path=libs/1.8.9/1.8.9-natives",
        "-Xmx2G"
    ]
}
