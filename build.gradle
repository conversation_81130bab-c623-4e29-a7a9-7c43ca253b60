plugins {
    id 'java'
    id 'application'
}

group = 'client.cryptix'
version = '1.0'

sourceCompatibility = JavaVersion.VERSION_1_8
targetCompatibility = JavaVersion.VERSION_1_8

mainClassName = 'net.minecraft.client.main.Main' // vagy a saját start class

repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    implementation files('libs/1.8.9/1.8.9.jar')
    implementation files('libs/org/lwjgl/lwjgl/lwjgl/2.9.4-nightly-20150209/lwjgl-2.9.4-nightly-20150209.jar')
    implementation files('libs/org/lwjgl/lwjgl/lwjgl_util/2.9.4-nightly-20150209/lwjgl_util-2.9.4-nightly-20150209.jar')
    implementation files('libs/net/java/jinput/jinput/2.0.5/jinput-2.0.5.jar')
    implementation files('libs/com/google/code/gson/gson/2.2.4/gson-2.2.4.jar')
    implementation files('libs/com/google/guava/guava/17.0/guava-17.0.jar')
    implementation files('libs/commons-codec/commons-codec/1.9/commons-codec-1.9.jar')
    implementation files('libs/commons-io/commons-io/2.4/commons-io-2.4.jar')
    implementation files('libs/commons-logging/commons-logging/1.1.3/commons-logging-1.1.3.jar')
}

application {
    applicationDefaultJvmArgs = [
        "-Djava.library.path=libs/natives",
        "-Xmx2G"
    ]
}
