plugins {
    id 'java'
    id 'application'
}

group = 'client.cryptix'
version = '1.0'

sourceCompatibility = JavaVersion.VERSION_1_8
targetCompatibility = JavaVersion.VERSION_1_8

mainClassName = 'net.minecraft.client.main.Main' // vagy a saját start class

repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    implementation name: '1.8.8'
    implementation name: 'lwjgl'
    implementation name: 'lwjgl_util'
    implementation name: 'jinput'
}

application {
    applicationDefaultJvmArgs = [
        "-Djava.library.path=libs/natives",
        "-Xmx2G"
    ]
}
