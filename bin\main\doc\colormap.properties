###############################################################################
# Sample configuration for OptiFine's Custom Colors feature.
# Based on the configuration for MCPatcher's Custom Colors mod.
#
###############################################################################
# colormap.properties
###############################################################################
# Sample colormap.properties file for use with MCPatcher's Custom Colors mod.
# Each custom colormap must have a properties file, except for "fixed"
# colormaps like pine.png or underwater.png.
#
# This file is offered without any copyright restrictions. Please copy and
# modify it to suit your needs.  Then place it in the mcpatcher/colormap/blocks
# folder or any subfolder within.
#
# All property names are case-sensitive.
# All colors are in hex rgb format, 000000 to ffffff.
# All paths are relative to assets/minecraft unless otherwise stated.
###############################################################################

###############################################################################
# Format of colormap.
###############################################################################
# fixed:   Single fixed color, no image required.  Useful for removing vanilla
#          biome coloring without having to create a 256x256 all-white
#          colormap.
# vanilla: Vanilla temperature+humidity 256x256 map.  Limited by the fact that
#          different biomes share the same base temp+humidity values.
# grid:    MCPatcher-style grid (x=biome ID, y=height) 256x256 map.  Each
#          column represents a single biome from the void to the max build
#          height.  Unused columns should be filled in with a reasonable
#          default gradient of colors to accommodate biomes added by mods.
# The default format is vanilla, but this can be overridden globally by setting
# palette.format in ~/color.properties.
format=<fixed | vanilla | grid>

###############################################################################
# (Optional) List of blocks (with optional metadata) to apply the colormap to.
###############################################################################
# In 1.7, blocks are referred to by name rather than number.  Numerical IDs are
# supported for older blocks, but newer blocks will be name only.  Stone blocks
# for example can be referred to as
#   1               (not recommended)
#   minecraft:stone (full name)
#   stone           (name without prefix)
#
# To color all stone and ore blocks:
#   blocks=stone gold_ore iron_ore coal_ore lapis_ore diamond_ore redstone_ore lit_redstone_ore
#
# Block + metadata is also supported using the following syntax:
#   blocks=sandstone double_stone_slab:1 stone_slab:1
# Applies to sandstone blocks and slabs.
#
# Metadata values for the same block can be grouped together:
#   blocks=leaves:3,7,11,15
# Applies to jungle leaves.
#
# If not set (or if there is no properties file), it defaults based on the
# filename, e.g.,
#   assets/minecraft/mcpatcher/colormap/custom/stone.png -> minecraft:stone
blocks=<list of blocks + optional metadata>

###############################################################################
# (format=fixed or vanilla only) Color map image.
###############################################################################
# Path can be relative to the location of the properties file.  The image
# should be a 256x256 color map.  If no source is given, a png with the same
# name as the properties file is used as a default.
source=<image>

###############################################################################
# (Optional) Fixed RGB color.
###############################################################################
# For format=fixed, this is simply the fixed color to be applied to all
# matching blocks.  If no value is given, the default is white (ffffff).
#
# For format=vanilla or grid, this is the default color used for held or
# dropped blocks.  If no value is given, the default color is instead taken
# from a fixed location depending on the format:
#   format=vanilla: x=127,y=127 (center of bitmap)
#   format=grid:    x=1,y=64    (plains biome at sea level)
color=<rgb value in hex>

###############################################################################
# (format=grid only) y variance value.
###############################################################################
# This adds a configurable amount of random noise to the y coordinate before it
# is used in the colormap.  For example a value of 2.0 will choose a value
# from the the colormap from y - 2 to y + 2.
# The default is 0 (no variance).
yVariance=<value>

###############################################################################
# (format=grid only) y offset value.
###############################################################################
# This subtracts a fixed value from the block's y coordinate before sampling
# from the colormap.  For example a value of 64 will use the pixel at 0 for
# blocks between layer 0 and 64.  A block at 65 will use pixel 1, 66 pixel 2,
# etc.
# The default is 0 (no offset).
yOffset=<value>